"""
Reminder Agent for Lumina AI

Handles scheduling, reminders, and task management.
"""

import asyncio
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import re

from agents.base.agent_interface import (
    BaseAgent,
    AgentRequest,
    AgentResponse,
    AgentCapability,
)
from config.settings import get_settings
from utils.logging import get_logger

logger = get_logger(__name__)


class ReminderAgent(BaseAgent):
    """
    Agent for handling reminders, scheduling, and task management
    """

    def __init__(self):
        super().__init__()
        self.settings = get_settings()
        self.name = "reminder"
        self.description = "Handles reminders, scheduling, and task management"
        self.version = "1.0.0"

        # Supported capabilities
        self.capabilities = [
            AgentCapability.TASK_MANAGEMENT,
            AgentCapability.SCHEDULING,
        ]

        # Keywords for intent detection
        self.keywords = {
            "hi": ["याद", "रिमाइंडर", "समय", "अलार्म", "कार्य", "काम", "मीटिंग", "अपॉइंटमेंट"],
            "en": [
                "remind",
                "reminder",
                "schedule",
                "alarm",
                "task",
                "meeting",
                "appointment",
                "todo",
            ],
            "mr": ["आठवण", "रिमाइंडर", "वेळ", "अलार्म", "कार्य", "काम", "मीटिंग"],
            "gu": ["યાદ", "રિમાઇન્ડર", "સમય", "અલાર્મ", "કાર્ય", "કામ", "મીટિંગ"],
            "ta": ["நினைவூட்டல்", "நேரம்", "அலாரம்", "பணி", "வேலை", "சந்திப்பு"],
            "te": ["గుర్తుచేయు", "సమయం", "అలారం", "పని", "పనిముట్టు", "సమావేశం"],
        }

        # Time patterns for different languages
        self.time_patterns = {
            "hi": {
                "minutes": r"(\d+)\s*(मिनट|मिनिट)",
                "hours": r"(\d+)\s*(घंटा|घंटे)",
                "days": r"(\d+)\s*(दिन)",
                "tomorrow": r"कल",
                "today": r"आज",
            },
            "en": {
                "minutes": r"(\d+)\s*(minute|minutes|min)",
                "hours": r"(\d+)\s*(hour|hours|hr)",
                "days": r"(\d+)\s*(day|days)",
                "tomorrow": r"tomorrow",
                "today": r"today",
            },
        }

    async def initialize(self) -> bool:
        """Initialize the reminder agent"""
        logger.info("Initializing Reminder Agent")

        try:
            # Initialize storage for reminders
            # TODO: Implement persistent storage
            logger.info("Reminder agent initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize reminder agent: {str(e)}")
            return False

    async def process_request(self, request: AgentRequest) -> AgentResponse:
        """Process a reminder-related request"""
        try:
            user_input = request.user_input.lower()
            language = request.language

            # Determine the type of reminder request
            request_type = self._classify_request(user_input, language)

            if request_type == "create":
                return await self._create_reminder(request)
            elif request_type == "list":
                return await self._list_reminders(request)
            elif request_type == "delete":
                return await self._delete_reminder(request)
            else:
                return self._get_help_response(language)

        except Exception as e:
            logger.error(f"Error processing reminder request: {str(e)}")
            return AgentResponse(
                content=self._get_error_message(request.language),
                success=False,
                agent_name=self.name,
                timestamp=datetime.now(),
                error=str(e),
            )

    def can_handle_request(self, request: AgentRequest) -> float:
        """Check if this agent can handle the request"""
        user_input = request.user_input.lower()
        language = request.language

        # Check for reminder-related keywords
        keywords = self.keywords.get(language, self.keywords["en"])

        for keyword in keywords:
            if keyword in user_input:
                return 0.9  # High confidence for reminder keywords

        # Check for time-related patterns
        time_patterns = self.time_patterns.get(language, self.time_patterns["en"])
        for pattern_type, pattern in time_patterns.items():
            if re.search(pattern, user_input, re.IGNORECASE):
                return 0.7  # Medium-high confidence for time patterns

        return 0.0

    def _classify_request(self, user_input: str, language: str) -> str:
        """Classify the type of reminder request"""
        create_keywords = {
            "hi": ["याद दिला", "रिमाइंड कर", "अलार्म लगा", "समय पर बता"],
            "en": ["remind me", "set reminder", "schedule", "alarm", "notify me"],
        }

        list_keywords = {
            "hi": ["सभी याददाश्त", "रिमाइंडर दिखा", "क्या याद रखना है"],
            "en": [
                "show reminders",
                "list reminders",
                "what reminders",
                "my reminders",
            ],
        }

        delete_keywords = {
            "hi": ["रिमाइंडर हटा", "याददाश्त मिटा", "रद्द कर"],
            "en": ["delete reminder", "remove reminder", "cancel reminder"],
        }

        # Check for create patterns
        for keyword in create_keywords.get(language, create_keywords["en"]):
            if keyword in user_input:
                return "create"

        # Check for list patterns
        for keyword in list_keywords.get(language, list_keywords["en"]):
            if keyword in user_input:
                return "list"

        # Check for delete patterns
        for keyword in delete_keywords.get(language, delete_keywords["en"]):
            if keyword in user_input:
                return "delete"

        # Default to create if time patterns are found
        time_patterns = self.time_patterns.get(language, self.time_patterns["en"])
        for pattern_type, pattern in time_patterns.items():
            if re.search(pattern, user_input, re.IGNORECASE):
                return "create"

        return "help"

    async def _create_reminder(self, request: AgentRequest) -> AgentResponse:
        """Create a new reminder"""
        user_input = request.user_input
        language = request.language

        # Extract time and message
        reminder_time = self._extract_time(user_input, language)
        reminder_message = self._extract_message(user_input, language)

        if not reminder_time:
            return AgentResponse(
                content=self._get_time_prompt(language),
                success=True,
                agent_name=self.name,
                timestamp=datetime.now(),
                metadata={"requires_time": True},
            )

        if not reminder_message:
            reminder_message = self._get_default_message(language)

        # Create reminder (mock implementation)
        reminder_id = f"rem_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # TODO: Store reminder in persistent storage

        response_text = self._format_reminder_created(
            reminder_time, reminder_message, language
        )

        return AgentResponse(
            content=response_text,
            success=True,
            agent_name=self.name,
            timestamp=datetime.now(),
            metadata={
                "reminder_id": reminder_id,
                "reminder_time": reminder_time.isoformat(),
                "message": reminder_message,
            },
        )

    async def _list_reminders(self, request: AgentRequest) -> AgentResponse:
        """List all reminders"""
        language = request.language

        # TODO: Fetch reminders from storage
        # Mock response for now
        reminders = []

        if not reminders:
            response_text = self._get_no_reminders_message(language)
        else:
            response_text = self._format_reminders_list(reminders, language)

        return AgentResponse(
            content=response_text,
            success=True,
            agent_name=self.name,
            timestamp=datetime.now(),
            metadata={"reminder_count": len(reminders)},
        )

    async def _delete_reminder(self, request: AgentRequest) -> AgentResponse:
        """Delete a reminder"""
        language = request.language

        # TODO: Implement reminder deletion
        response_text = self._get_delete_confirmation(language)

        return AgentResponse(
            content=response_text,
            success=True,
            agent_name=self.name,
            timestamp=datetime.now(),
        )

    def _extract_time(self, user_input: str, language: str) -> Optional[datetime]:
        """Extract time from user input"""
        user_input = user_input.lower()
        time_patterns = self.time_patterns.get(language, self.time_patterns["en"])

        now = datetime.now()

        # Check for relative time patterns
        for pattern_type, pattern in time_patterns.items():
            match = re.search(pattern, user_input, re.IGNORECASE)
            if match:
                if pattern_type == "minutes":
                    minutes = int(match.group(1))
                    return now + timedelta(minutes=minutes)
                elif pattern_type == "hours":
                    hours = int(match.group(1))
                    return now + timedelta(hours=hours)
                elif pattern_type == "days":
                    days = int(match.group(1))
                    return now + timedelta(days=days)
                elif pattern_type == "tomorrow":
                    return now + timedelta(days=1)
                elif pattern_type == "today":
                    return now + timedelta(hours=1)  # Default to 1 hour from now

        return None

    def _extract_message(self, user_input: str, language: str) -> str:
        """Extract reminder message from user input"""
        # Simple extraction - remove time-related words and common phrases
        message = user_input

        # Remove common reminder phrases
        remove_phrases = {
            "hi": ["याद दिला", "रिमाइंड कर", "अलार्म लगा"],
            "en": ["remind me", "set reminder", "schedule", "alarm"],
        }

        phrases = remove_phrases.get(language, remove_phrases["en"])
        for phrase in phrases:
            message = message.replace(phrase, "").strip()

        # Remove time patterns
        time_patterns = self.time_patterns.get(language, self.time_patterns["en"])
        for pattern in time_patterns.values():
            message = re.sub(pattern, "", message, flags=re.IGNORECASE).strip()

        return message if message else ""

    def _get_help_response(self, language: str) -> AgentResponse:
        """Get help response for reminder commands"""
        help_messages = {
            "hi": "मैं आपके लिए रिमाइंडर सेट कर सकता हूं। उदाहरण: '5 मिनट में दवा लेने की याद दिला दो' या 'कल मीटिंग की याद दिला दो'",
            "en": "I can set reminders for you. Examples: 'Remind me to take medicine in 5 minutes' or 'Remind me about the meeting tomorrow'",
            "mr": "मी तुमच्यासाठी रिमाइंडर सेट करू शकतो. उदाहरण: '५ मिनिटांत औषध घेण्याची आठवण करून द्या'",
            "gu": "હું તમારા માટે રિમાઇન્ડર સેટ કરી શકું છું. ઉદાહરણ: '5 મિનિટમાં દવા લેવાની યાદ અપાવો'",
            "ta": "நான் உங்களுக்கு நினைவூட்டல்களை அமைக்க முடியும். உதாரணம்: '5 நிமிடத்தில் மருந்து எடுக்க நினைவூட்டுங்கள்'",
            "te": "నేను మీ కోసం రిమైండర్లు సెట్ చేయగలను. ఉదాహరణ: '5 నిమిషాల్లో మందు తీసుకోవాలని గుర్తుచేయండి'",
        }

        return AgentResponse(
            content=help_messages.get(language, help_messages["en"]),
            success=True,
            agent_name=self.name,
            timestamp=datetime.now(),
            metadata={"help": True},
        )

    def _get_time_prompt(self, language: str) -> str:
        """Get time prompt message"""
        prompts = {
            "hi": "कृपया बताएं कि कब याद दिलाना है? जैसे '5 मिनट में' या 'कल'",
            "en": "Please tell me when to remind you? For example '5 minutes' or 'tomorrow'",
            "mr": "कृपया सांगा की कधी आठवण करून द्यायची? जसे '५ मिनिटांत' किंवा 'उद्या'",
            "gu": "કૃપા કરીને કહો કે ક્યારે યાદ અપાવવું? જેમ કે '5 મિનિટમાં' અથવા 'કાલે'",
            "ta": "எப்போது நினைவூட்ட வேண்டும் என்று சொல்லுங்கள்? உதாரணம் '5 நிமிடத்தில்' அல்லது 'நாளை'",
            "te": "ఎప్పుడు గుర్తుచేయాలో చెప్పండి? ఉదాహరణ '5 నిమిషాల్లో' లేదా 'రేపు'",
        }

        return prompts.get(language, prompts["en"])

    def _get_default_message(self, language: str) -> str:
        """Get default reminder message"""
        messages = {
            "hi": "रिमाइंडर",
            "en": "Reminder",
            "mr": "आठवण",
            "gu": "રિમાઇન્ડર",
            "ta": "நினைவூட்டல்",
            "te": "రిమైండర్",
        }

        return messages.get(language, messages["en"])

    def _format_reminder_created(
        self, reminder_time: datetime, message: str, language: str
    ) -> str:
        """Format reminder creation confirmation"""
        time_str = reminder_time.strftime("%Y-%m-%d %H:%M")

        templates = {
            "hi": f"रिमाइंडर सेट हो गया! मैं आपको {time_str} पर '{message}' की याद दिला दूंगा।",
            "en": f"Reminder set! I'll remind you about '{message}' at {time_str}.",
            "mr": f"रिमाइंडर सेट झाला! मी तुम्हाला {time_str} वर '{message}' ची आठवण करून देईन.",
            "gu": f"રિમાઇન્ડર સેટ થયું! હું તમને {time_str} પર '{message}' ની યાદ અપાવીશ.",
            "ta": f"நினைவூட்டல் அமைக்கப்பட்டது! நான் உங்களுக்கு {time_str} அன்று '{message}' பற்றி நினைவூட்டுவேன்.",
            "te": f"రిమైండర్ సెట్ అయింది! నేను మీకు {time_str} కి '{message}' గుర్తుచేస్తాను.",
        }

        return templates.get(language, templates["en"])

    def _get_no_reminders_message(self, language: str) -> str:
        """Get no reminders message"""
        messages = {
            "hi": "आपके पास कोई रिमाइंडर नहीं है।",
            "en": "You don't have any reminders.",
            "mr": "तुमच्याकडे कोणतेही रिमाइंडर नाहीत.",
            "gu": "તમારી પાસે કોઈ રિમાઇન્ડર નથી.",
            "ta": "உங்களிடம் எந்த நினைவூட்டல்களும் இல்லை.",
            "te": "మీ వద్ద ఎలాంటి రిమైండర్లు లేవు.",
        }

        return messages.get(language, messages["en"])

    def _format_reminders_list(self, reminders: List[Dict], language: str) -> str:
        """Format list of reminders"""
        # TODO: Implement reminder list formatting
        return "Reminders list formatting not implemented yet"

    def _get_delete_confirmation(self, language: str) -> str:
        """Get delete confirmation message"""
        messages = {
            "hi": "रिमाइंडर डिलीट करने की सुविधा जल्द ही उपलब्ध होगी।",
            "en": "Reminder deletion feature will be available soon.",
            "mr": "रिमाइंडर डिलीट करण्याची सुविधा लवकरच उपलब्ध होईल.",
            "gu": "રિમાઇન્ડર ડિલીટ કરવાની સુવિધા ટૂંક સમયમાં ઉપલબ્ધ થશે.",
            "ta": "நினைவூட்டல் நீக்கும் வசதி விரைவில் கிடைக்கும்.",
            "te": "రిమైండర్ తొలగించే సౌకర్యం త్వరలో అందుబాటులో ఉంటుంది.",
        }

        return messages.get(language, messages["en"])

    def _get_error_message(self, language: str) -> str:
        """Get error message"""
        messages = {
            "hi": "रिमाइंडर सेट करने में समस्या हो रही है। कृपया बाद में कोशिश करें।",
            "en": "I'm having trouble setting the reminder. Please try again later.",
            "mr": "रिमाइंडर सेट करण्यात अडचण येत आहे. कृपया नंतर प्रयत्न करा.",
            "gu": "રિમાઇન્ડર સેટ કરવામાં મુશ્કેલી આવી રહી છે. કૃપા કરીને પછીથી પ્રયાસ કરો.",
            "ta": "நினைவூட்டல் அமைப்பதில் சிக்கல் உள்ளது. பின்னர் முயற்சிக்கவும்.",
            "te": "రిమైండర్ సెట్ చేయడంలో ఇబ్బంది ఉంది. దయచేసి తర్వాత ప్రయత్నించండి.",
        }

        return messages.get(language, messages["en"])
