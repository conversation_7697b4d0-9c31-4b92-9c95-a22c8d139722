#!/usr/bin/env python3
"""
Lumina AI - Test Version
Main application entry point with limited runtime for testing
"""
import asyncio
import logging
import sys
from pathlib import Path

from core.app import <PERSON><PERSON>App
from utils.logging import setup_logging
from config.settings import load_settings

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


async def main():
    """Main application entry point with test mode"""
    try:
        # Load configuration first to get debug mode
        settings = load_settings()

        # Setup logging with debug mode from settings
        debug_mode = settings.app.get("debug", False)
        logging_config = settings.monitoring.get("logging", {})

        setup_logging(
            level=logging_config.get("level", "INFO"),
            log_format=logging_config.get("format", "json"),
            log_file=logging_config.get("file", "logs/lumina.log"),
            max_file_size=logging_config.get("max_file_size", "10MB"),
            backup_count=logging_config.get("backup_count", 5),
            debug_mode=debug_mode,
        )

        logger = logging.getLogger(__name__)

        logger.info("Starting Lumina AI in TEST MODE...")

        # Initialize and start the application
        app = LuminaApp(settings)
        await app.initialize()

        logger.info("Lumina AI started successfully")

        # Run the application for a limited time (10 seconds for testing)
        logger.info("Running Lumina AI for 10 seconds in test mode...")

        # Start voice handler
        await app.voice_handler.start()

        # Start metrics collection if enabled
        if app.settings.monitoring.get("metrics", {}).get("enabled", False):
            asyncio.create_task(app.metrics.start_collection())

        # Test mode: run for 10 seconds then exit
        test_duration = 10
        for i in range(test_duration):
            logger.info(f"Test mode running... {test_duration - i} seconds remaining")
            await asyncio.sleep(1)

        logger.info("Test mode completed successfully!")
        logger.info("✅ Lumina AI is working correctly!")
        logger.info("🎉 To run in production mode, use: python main.py")

    except KeyboardInterrupt:
        logger.info("Received shutdown signal")
    except (ImportError, ModuleNotFoundError) as e:
        logger.error(f"Missing dependency: {e}")
        sys.exit(1)
    except (ValueError, TypeError, AttributeError) as e:
        logger.error(f"Configuration or initialization error: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
    finally:
        if "app" in locals():
            await app.cleanup()
        logger.info("Lumina AI test shutdown complete")


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())
