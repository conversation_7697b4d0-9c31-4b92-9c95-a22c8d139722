# Lumina AI 🌟

A lightweight, modular voice assistant built in Python that delivers fast, reliable, and natural conversational experiences, primarily in Hindi.

## 🚀 Features

- **Multilingual Voice Interaction**: Native support for Hindi, English, Marathi, Gujarati, Tamil, and Telugu
- **Agent-Based Architecture**: Modular task-specific AI agents for weather, reminders, news, summarization, and translation
- **Multiple LLM Providers**: Integration with Google Gemini, Groq, and OpenRouter with intelligent fallback
- **Real-time Voice Processing**: LiveKit integration for low-latency voice interaction
- **Edge TTS Integration**: High-quality text-to-speech using Microsoft Edge TTS
- **Scalable Design**: Clean architecture supporting easy addition of new languages and agents

## 📁 Project Structure

```
lumina_ai/
├── main.py                          # Application entry point
├── requirements.txt                 # Python dependencies
├── config/                          # Configuration files
│   ├── settings.yaml               # Main application settings
│   ├── languages.yaml              # Language-specific configurations
│   └── agents.yaml                 # Agent definitions and routing
├── core/                           # Core application logic
│   ├── app.py                      # Main application orchestrator
│   ├── context_manager.py          # Conversation context & memory
│   └── exceptions.py               # Custom exception classes
├── voice/                          # Voice processing components
│   ├── input/                      # Voice input handling
│   ├── output/                     # Voice synthesis
│   └── processors/                 # Audio processing utilities
├── llm/                           # Language model integration
│   ├── providers/                  # LLM provider implementations
│   ├── router.py                   # LLM routing & fallback logic
│   └── cache.py                    # Response caching system
├── agents/                        # AI agents
│   ├── base/                      # Base agent framework
│   ├── weather/                   # Weather information agent
│   ├── reminder/                  # Reminder management agent
│   ├── news/                      # News fetching agent
│   ├── summarization/             # Text summarization agent
│   └── translation/               # Language translation agent
├── localization/                  # Language-specific resources
│   ├── hindi/                     # Hindi language pack
│   └── english/                   # English language pack
├── utils/                         # Utility modules
├── storage/                       # Data storage implementations
├── api/                          # REST and WebSocket APIs
├── tests/                        # Test suite
└── docs/                         # Documentation
```

## 🛠️ Installation

1. **Clone the repository**:

   ```bash
   git clone <repository-url>
   cd lumina_ai
   ```

2. **Create virtual environment**:

   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:

   ```bash
   pip install -r requirements.txt
   ```

4. **Install voice dependencies** (for microphone input):

   ```bash
   python install_voice_dependencies.py
   ```

5. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

## ⚙️ Configuration

### Required API Keys

- **Google Gemini**: `GEMINI_API_KEY`
- **Groq**: `GROQ_API_KEY`
- **OpenRouter**: `OPENROUTER_API_KEY`
- **Weather**: `WEATHER_API_KEY` (OpenWeatherMap)
- **News**: `NEWS_API_KEY`
- **LiveKit**: `LIVEKIT_API_KEY`, `LIVEKIT_API_SECRET`

### Optional Services

- **Azure Speech Services**: For enhanced STT/TTS
- **Redis**: For distributed caching
- **PostgreSQL**: For persistent storage

## 🚀 Quick Start

1. **Start the application**:

   ```bash
   python main.py
   ```

2. **Test voice interaction**:
   - Connect to the LiveKit room
   - Speak in Hindi or English
   - Receive voice responses

## 🏗️ Architecture

Lumina AI follows a clean, modular architecture:

- **Voice Layer**: Handles audio input/output and speech processing
- **LLM Layer**: Manages multiple language model providers with fallback
- **Agent Layer**: Task-specific AI agents for different capabilities
- **Storage Layer**: Conversation memory and user preferences
- **API Layer**: REST and WebSocket interfaces for external integration

## 🌐 Supported Languages

- **Hindi (हिंदी)**: Primary language with full feature support
- **English**: Complete feature support
- **Marathi (मराठी)**: Voice and text support
- **Gujarati (ગુજરાતી)**: Voice and text support
- **Tamil (தமிழ்)**: Voice and text support
- **Telugu (తెలుగు)**: Voice and text support

## 🤖 Available Agents

- **Weather Agent**: Real-time weather information and forecasts
- **Reminder Agent**: Schedule and manage reminders
- **News Agent**: Latest news updates in preferred language
- **Summarization Agent**: Text summarization and content processing
- **Translation Agent**: Multi-language translation support
- **General Agent**: Handles general queries and conversations

## 📊 Monitoring

- **Prometheus Metrics**: Performance and usage metrics
- **Health Checks**: Component status monitoring
- **Structured Logging**: JSON-formatted logs for analysis
- **Real-time Dashboards**: System performance visualization

## 🧪 Testing

```bash
# Run all tests
pytest

# Run specific test categories
pytest tests/unit/
pytest tests/integration/

# Run with coverage
pytest --cov=lumina_ai
```

## 📚 Documentation

- [Architecture Guide](docs/architecture.md)
- [Agent Development](docs/agent_development.md)
- [API Reference](docs/api_reference.md)
- [Deployment Guide](docs/deployment.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **LiveKit**: Real-time voice communication
- **Microsoft Edge TTS**: High-quality text-to-speech
- **Google Gemini**: Primary language model
- **OpenWeatherMap**: Weather data API
- **Indic NLP Library**: Indian language processing

## 🧪 Testing

Run the test suite to verify your installation:

```bash
python test_lumina.py
```

## 🔧 Troubleshooting

### Voice Input Issues

If you encounter errors like `[WinError 2] The system cannot find the file specified` during voice transcription:

1. **Quick Fix**: Run the voice dependencies installer:

   ```bash
   python install_voice_dependencies.py
   ```

2. **Manual Fix**: Install PyAudio:

   ```bash
   pip install pyaudio
   ```

3. **Temporary Workaround**: Disable voice input in `config/settings.yaml`:
   ```yaml
   voice:
     input:
       provider: "livekit" # Disables microphone input
   ```

For detailed troubleshooting, see **[VOICE_TROUBLESHOOTING.md](VOICE_TROUBLESHOOTING.md)**

## 📚 Documentation

- **API Documentation**: See `docs/api.md`
- **Configuration Guide**: See `docs/configuration.md`
- **Development Guide**: See `docs/development.md`
- **Voice Troubleshooting**: See `VOICE_TROUBLESHOOTING.md`

---

**Lumina AI** - Bringing natural voice interaction to Indian languages 🇮🇳
