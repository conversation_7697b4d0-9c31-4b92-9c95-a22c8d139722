#!/usr/bin/env python3
"""
Lumina AI Voice Dependencies Installer
Installs required dependencies for voice functionality
"""

import subprocess
import sys
import os
import platform
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Command: {command}")
        print(f"   Error: {e.stderr}")
        return False

def install_pyaudio_windows():
    """Install PyAudio on Windows"""
    print("🪟 Detected Windows - Installing PyAudio...")
    
    # Try pip install first
    if run_command("pip install pyaudio", "Installing PyAudio via pip"):
        return True
    
    # If that fails, try with pre-compiled wheel
    print("⚠️ Standard PyAudio installation failed. Trying alternative method...")
    
    # Get Python version
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}"
    architecture = "win_amd64" if platform.machine().endswith('64') else "win32"
    
    print(f"📊 Python version: {python_version}, Architecture: {architecture}")
    
    # Try installing from unofficial binaries
    wheel_url = f"https://download.lfd.uci.edu/pythonlibs/archived/PyAudio-0.2.11-cp{python_version.replace('.', '')}-cp{python_version.replace('.', '')}-{architecture}.whl"
    
    if run_command(f"pip install {wheel_url}", "Installing PyAudio from unofficial binaries"):
        return True
    
    print("❌ PyAudio installation failed. Manual installation required.")
    print("💡 Try one of these solutions:")
    print("   1. Install Visual Studio Build Tools")
    print("   2. Use conda: conda install pyaudio")
    print("   3. Download pre-compiled wheel from https://www.lfd.uci.edu/~gohlke/pythonlibs/#pyaudio")
    
    return False

def install_pyaudio_linux():
    """Install PyAudio on Linux"""
    print("🐧 Detected Linux - Installing PyAudio...")
    
    # Install system dependencies first
    distro_commands = [
        ("apt-get update && apt-get install -y portaudio19-dev python3-pyaudio", "Ubuntu/Debian"),
        ("yum install -y portaudio-devel", "CentOS/RHEL"),
        ("dnf install -y portaudio-devel", "Fedora"),
        ("pacman -S portaudio", "Arch Linux"),
    ]
    
    print("📦 Installing system dependencies...")
    for cmd, distro in distro_commands:
        print(f"   For {distro}: sudo {cmd}")
    
    print("⚠️ Please install the appropriate system dependencies for your distribution first.")
    
    # Then install PyAudio
    return run_command("pip install pyaudio", "Installing PyAudio via pip")

def install_pyaudio_macos():
    """Install PyAudio on macOS"""
    print("🍎 Detected macOS - Installing PyAudio...")
    
    # Install portaudio via homebrew first
    if run_command("brew install portaudio", "Installing PortAudio via Homebrew"):
        return run_command("pip install pyaudio", "Installing PyAudio via pip")
    else:
        print("❌ Homebrew not found or PortAudio installation failed")
        print("💡 Please install Homebrew first: /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
        return False

def install_whisper_dependencies():
    """Install Whisper and related dependencies"""
    print("🎤 Installing Whisper STT dependencies...")
    
    commands = [
        ("pip install openai-whisper", "Installing OpenAI Whisper"),
        ("pip install torch torchvision torchaudio", "Installing PyTorch"),
    ]
    
    success = True
    for cmd, desc in commands:
        if not run_command(cmd, desc):
            success = False
    
    return success

def install_edge_tts():
    """Install Edge TTS"""
    return run_command("pip install edge-tts", "Installing Microsoft Edge TTS")

def main():
    """Main installation function"""
    print("🌟 Lumina AI Voice Dependencies Installer")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        sys.exit(1)
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro} detected")
    
    # Detect operating system
    system = platform.system().lower()
    print(f"🖥️ Operating System: {system}")
    
    success = True
    
    # Install PyAudio based on OS
    if system == "windows":
        success &= install_pyaudio_windows()
    elif system == "linux":
        success &= install_pyaudio_linux()
    elif system == "darwin":  # macOS
        success &= install_pyaudio_macos()
    else:
        print(f"⚠️ Unsupported operating system: {system}")
        success = False
    
    # Install other dependencies
    if success:
        success &= install_whisper_dependencies()
        success &= install_edge_tts()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All voice dependencies installed successfully!")
        print("✅ You can now use voice input and output features")
        print("🚀 Run 'python main.py' to start Lumina AI with voice support")
    else:
        print("❌ Some dependencies failed to install")
        print("💡 Please check the error messages above and install manually")
        print("📚 See README.md for detailed installation instructions")
    
    print("\n📝 Next steps:")
    print("   1. Configure your API keys in .env file")
    print("   2. Test voice functionality: python test_lumina.py")
    print("   3. Start the application: python main.py")

if __name__ == "__main__":
    main()
