"""
LLM Router for Lumina AI

Handles routing requests to different LLM providers with fallback logic,
load balancing, and circuit breaker patterns.
"""

import asyncio
import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from enum import Enum

from llm.providers.base_provider import BaseLL<PERSON>rovider, LLMRequest, LLMResponse
from llm.providers.gemini_provider import GeminiProvider
from llm.providers.groq_provider import GroqProvider
from llm.cache import LL<PERSON>ache
from config.settings import get_settings
from utils.logging import get_logger

logger = get_logger(__name__)


class ProviderStatus(Enum):
    """Provider health status"""

    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"


class CircuitBreakerState(Enum):
    """Circuit breaker states"""

    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"


class ProviderCircuitBreaker:
    """Circuit breaker for LLM providers"""

    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitBreakerState.CLOSED

    def record_success(self):
        """Record a successful request"""
        self.failure_count = 0
        self.state = CircuitBreakerState.CLOSED

    def record_failure(self):
        """Record a failed request"""
        self.failure_count += 1
        self.last_failure_time = datetime.now()

        if self.failure_count >= self.failure_threshold:
            self.state = CircuitBreakerState.OPEN

    def can_attempt(self) -> bool:
        """Check if requests can be attempted"""
        if self.state == CircuitBreakerState.CLOSED:
            return True

        if self.state == CircuitBreakerState.OPEN:
            if (
                datetime.now() - self.last_failure_time
            ).seconds >= self.recovery_timeout:
                self.state = CircuitBreakerState.HALF_OPEN
                return True
            return False

        # HALF_OPEN state
        return True


class LLMRouter:
    """
    Routes LLM requests to appropriate providers with fallback logic
    """

    def __init__(self, cache: Optional[LLMCache] = None):
        self.settings = get_settings()
        self.cache = cache

        # Initialize providers
        self.providers: Dict[str, BaseLLMProvider] = {
            "gemini": GeminiProvider(),
            "groq": GroqProvider(),
        }

        # Circuit breakers for each provider
        self.circuit_breakers: Dict[str, ProviderCircuitBreaker] = {
            name: ProviderCircuitBreaker() for name in self.providers.keys()
        }

        # Provider health status
        self.provider_status: Dict[str, ProviderStatus] = {
            name: ProviderStatus.HEALTHY for name in self.providers.keys()
        }

        # Provider priority order
        self.provider_priority = self.settings.llm.get(
            "provider_priority", ["gemini", "groq", "openrouter"]
        )

        # Health check interval
        self.health_check_interval = 300  # 5 minutes
        self.last_health_check = datetime.now()

    async def initialize(self) -> bool:
        """Initialize all LLM providers"""
        try:
            logger.info("Initializing LLM providers...")

            for name, provider in self.providers.items():
                try:
                    await provider.initialize()
                    logger.info(f"✓ {name} provider initialized")
                except Exception as e:
                    logger.warning(f"✗ Failed to initialize {name} provider: {e}")
                    self.provider_status[name] = ProviderStatus.UNHEALTHY

            # Check if at least one provider is healthy
            healthy_providers = [
                name
                for name, status in self.provider_status.items()
                if status == ProviderStatus.HEALTHY
            ]

            if healthy_providers:
                logger.info(
                    f"LLM Router initialized with {len(healthy_providers)} healthy providers"
                )
                return True
            else:
                logger.warning(
                    "No healthy LLM providers available - running in degraded mode"
                )
                return True  # Allow initialization even without healthy providers

        except Exception as e:
            logger.error(f"Failed to initialize LLM Router: {e}")
            return False

    async def generate_response(self, request: LLMRequest) -> LLMResponse:
        """Generate response using the best available provider"""

        # Check cache first
        if self.cache:
            cached_response = await self.cache.get_cached_response(request)
            if cached_response:
                logger.debug(f"Cache hit for request: {request.session_id}")
                return cached_response

        # Perform health check if needed
        await self._periodic_health_check()

        # Get available providers in priority order
        available_providers = self._get_available_providers()

        if not available_providers:
            return LLMResponse(
                content="No LLM providers available",
                success=False,
                error="All providers are unhealthy",
                provider="router",
                model="unknown",
                timestamp=datetime.now(),
            )

        # Try providers in order
        last_error = None
        for provider_name in available_providers:
            provider = self.providers[provider_name]
            circuit_breaker = self.circuit_breakers[provider_name]

            if not circuit_breaker.can_attempt():
                logger.debug(f"Circuit breaker open for provider: {provider_name}")
                continue

            try:
                logger.debug(f"Attempting request with provider: {provider_name}")
                response = await provider.generate_response(request)

                if response.success:
                    circuit_breaker.record_success()
                    self.provider_status[provider_name] = ProviderStatus.HEALTHY

                    # Cache successful response
                    if self.cache:
                        await self.cache.cache_response(request, response)

                    logger.info(f"Successful response from provider: {provider_name}")
                    return response
                else:
                    circuit_breaker.record_failure()
                    last_error = response.error
                    logger.warning(
                        f"Provider {provider_name} returned error: {response.error}"
                    )

            except Exception as e:
                circuit_breaker.record_failure()
                last_error = str(e)
                logger.error(
                    f"Provider {provider_name} failed with exception: {str(e)}"
                )

        # All providers failed
        return LLMResponse(
            content="All LLM providers failed",
            success=False,
            error=f"Last error: {last_error}",
            provider="router",
            model="unknown",
            timestamp=datetime.now(),
        )

    def _get_available_providers(self) -> List[str]:
        """Get list of available providers in priority order"""
        available = []

        for provider_name in self.provider_priority:
            if (
                provider_name in self.providers
                and self.provider_status[provider_name] != ProviderStatus.UNHEALTHY
            ):
                available.append(provider_name)

        # Add any remaining healthy providers
        for provider_name, status in self.provider_status.items():
            if provider_name not in available and status != ProviderStatus.UNHEALTHY:
                available.append(provider_name)

        return available

    async def _periodic_health_check(self):
        """Perform periodic health checks on providers"""
        now = datetime.now()
        if (now - self.last_health_check).seconds < self.health_check_interval:
            return

        logger.info("Performing provider health checks")

        for provider_name, provider in self.providers.items():
            try:
                is_healthy = await provider.check_health()
                if is_healthy:
                    self.provider_status[provider_name] = ProviderStatus.HEALTHY
                    logger.debug(f"Provider {provider_name} is healthy")
                else:
                    self.provider_status[provider_name] = ProviderStatus.UNHEALTHY
                    logger.warning(f"Provider {provider_name} is unhealthy")

            except Exception as e:
                self.provider_status[provider_name] = ProviderStatus.UNHEALTHY
                logger.error(f"Health check failed for {provider_name}: {str(e)}")

        self.last_health_check = now

    async def get_provider_stats(self) -> Dict[str, Any]:
        """Get statistics for all providers"""
        stats = {}

        for provider_name, provider in self.providers.items():
            circuit_breaker = self.circuit_breakers[provider_name]

            stats[provider_name] = {
                "status": self.provider_status[provider_name].value,
                "circuit_breaker_state": circuit_breaker.state.value,
                "failure_count": circuit_breaker.failure_count,
                "last_failure": (
                    circuit_breaker.last_failure_time.isoformat()
                    if circuit_breaker.last_failure_time
                    else None
                ),
                "provider_info": provider.get_provider_info(),
                "rate_limits": provider.get_rate_limits(),
            }

        return stats

    async def force_health_check(self) -> Dict[str, bool]:
        """Force health check on all providers"""
        results = {}

        for provider_name, provider in self.providers.items():
            try:
                is_healthy = await provider.check_health()
                results[provider_name] = is_healthy

                if is_healthy:
                    self.provider_status[provider_name] = ProviderStatus.HEALTHY
                else:
                    self.provider_status[provider_name] = ProviderStatus.UNHEALTHY

            except Exception as e:
                results[provider_name] = False
                self.provider_status[provider_name] = ProviderStatus.UNHEALTHY
                logger.error(
                    f"Forced health check failed for {provider_name}: {str(e)}"
                )

        self.last_health_check = datetime.now()

    async def cleanup(self):
        """Cleanup LLM router resources"""
        try:
            logger.info("Cleaning up LLM Router")

            # Cleanup all providers
            for name, provider in self.providers.items():
                try:
                    if hasattr(provider, "cleanup"):
                        await provider.cleanup()
                    logger.info(f"✓ {name} provider cleaned up")
                except Exception as e:
                    logger.warning(f"Error cleaning up {name} provider: {e}")

            # Clear caches and state
            if hasattr(self, "cache") and self.cache is not None:
                self.cache.clear()

            self.providers.clear()
            self.provider_status.clear()

            logger.info("LLM Router cleanup completed")

        except Exception as e:
            logger.error(f"Error during LLM Router cleanup: {e}")
