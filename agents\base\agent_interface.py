"""
Base agent interface and data structures for Lumina AI
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum
import logging


class AgentCapability(Enum):
    """Enumeration of agent capabilities"""

    WEATHER = "weather"
    REMINDER = "reminder"
    SUMMARIZATION = "summarization"
    NEWS = "news"
    TRANSLATION = "translation"
    GENERAL_QUERY = "general_query"
    INFORMATION_RETRIEVAL = "information_retrieval"
    REAL_TIME_DATA = "real_time_data"
    TASK_MANAGEMENT = "task_management"
    SCHEDULING = "scheduling"


@dataclass
class AgentRequest:
    """Request data structure for agents"""

    user_input: str
    session_id: str
    language: str = "en"
    timestamp: Optional[Any] = None
    conversation_history: Optional[List[Dict[str, Any]]] = None
    user_preferences: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class AgentResponse:
    """Response data structure from agents"""

    content: str
    success: bool
    agent_name: str
    timestamp: Any
    audio_response: Optional[bytes] = None
    metadata: Optional[Dict[str, Any]] = None
    requires_followup: bool = False
    confidence_score: float = 1.0
    processing_time: float = 0.0
    error: Optional[str] = None


class BaseAgent(ABC):
    """Abstract base class for all Lumina AI agents"""

    def __init__(self):
        self.name = ""
        self.description = ""
        self.version = "1.0.0"
        self.capabilities: List[AgentCapability] = []
        self.is_active = True
        self._initialized = False
        self.config: Dict[str, Any] = {}
        self.logger = logging.getLogger(f"agents.{self.name or 'base'}")

    @abstractmethod
    async def process_request(self, request: AgentRequest) -> AgentResponse:
        """Process a request and return response"""
        ...

    @abstractmethod
    def can_handle_request(self, request: AgentRequest) -> float:
        """Check if this agent can handle the request"""
        ...

    async def initialize(self) -> bool:
        """Initialize agent resources"""
        try:
            self._initialized = True
            return True
        except (RuntimeError, ValueError, TypeError) as e:
            self.logger.error("Failed to initialize agent %s: %s", self.name, e)
            return False

    async def cleanup(self) -> None:
        """Cleanup agent resources"""
        try:
            self._initialized = False
        except (RuntimeError, ValueError) as e:
            self.logger.warning("Error during agent cleanup: %s", e)

    def get_name(self) -> str:
        """Get agent name"""
        return self.name

    def get_description(self) -> str:
        """Get agent description"""
        return self.description

    def get_capabilities(self) -> List[AgentCapability]:
        """Get agent capabilities"""
        return self.capabilities.copy()

    def is_initialized(self) -> bool:
        """Check if agent is initialized"""
        return self._initialized

    def get_config(self) -> Dict[str, Any]:
        """Get agent configuration"""
        return self.config.copy()

    def set_active(self, active: bool) -> None:
        """Set agent active status"""
        self.is_active = active
        self.logger.info(
            "Agent %s %s", self.name, "activated" if active else "deactivated"
        )

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on agent"""
        return {
            "name": self.name,
            "is_active": self.is_active,
            "is_initialized": self._initialized,
            "capabilities": [cap.value for cap in self.capabilities],
            "status": (
                "healthy" if self.is_active and self._initialized else "unhealthy"
            ),
        }

    def __str__(self) -> str:
        return f"Agent({self.name}, capabilities={[cap.value for cap in self.capabilities]})"

    def __repr__(self) -> str:
        return self.__str__()


class AgentMetrics:
    """Metrics tracking for agents"""

    def __init__(self, agent_name: str):
        self.agent_name = agent_name
        self.request_count = 0
        self.total_processing_time = 0.0
        self.error_count = 0
        self.confidence_scores = []

    def record_request(
        self, processing_time: float, confidence_score: float, had_error: bool = False
    ):
        """Record metrics for a request"""
        self.request_count += 1
        self.total_processing_time += processing_time
        self.confidence_scores.append(confidence_score)

        if had_error:
            self.error_count += 1

    def get_average_processing_time(self) -> float:
        """Get average processing time"""
        return self.total_processing_time / max(self.request_count, 1)

    def get_average_confidence(self) -> float:
        """Get average confidence score"""
        return sum(self.confidence_scores) / max(len(self.confidence_scores), 1)

    def get_error_rate(self) -> float:
        """Get error rate"""
        return self.error_count / max(self.request_count, 1)

    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get metrics summary"""
        return {
            "agent_name": self.agent_name,
            "request_count": self.request_count,
            "average_processing_time": self.get_average_processing_time(),
            "average_confidence": self.get_average_confidence(),
            "error_rate": self.get_error_rate(),
            "total_processing_time": self.total_processing_time,
        }
