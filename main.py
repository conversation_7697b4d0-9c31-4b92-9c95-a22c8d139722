#!/usr/bin/env python3
"""
Lumina AI - Multilingual Voice Assistant
Main application entry point
"""
import asyncio
import logging
import sys
from pathlib import Path

from core.app import <PERSON><PERSON><PERSON><PERSON>
from utils.logging import setup_logging
from config.settings import load_settings

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


async def _display_startup_greeting(logger, debug_mode):
    """Display startup greeting and instructions"""
    import asyncio

    # Small delay to let initialization messages settle
    await asyncio.sleep(0.5)

    if not debug_mode:
        # User-friendly greeting for production mode
        logger.info("🎉 Welcome to Lumina AI - Your Multilingual Voice Assistant!")
        await asyncio.sleep(0.2)

        # Check voice input status from settings
        from config.settings import load_settings

        settings = load_settings()
        voice_provider = settings.voice.get("input", {}).get("provider", "livekit")

        if voice_provider == "microphone":
            logger.info("🎤 Voice Input: Microphone capture enabled (requires PyAudio)")
            logger.info("💡 Speak to interact with Lu<PERSON> AI!")
        else:
            logger.info("💡 Current Status: Voice input is in development mode")
            logger.info(
                "🔧 To enable voice input: Set provider to 'microphone' or configure LiveKit"
            )

        await asyncio.sleep(0.2)
        logger.info("✨ Lumina AI is ready for conversations!")
        await asyncio.sleep(0.2)
        logger.info("📝 Press Ctrl+C to stop the application")
    else:
        # Technical greeting for debug mode
        logger.info("Lumina AI startup complete - Debug mode active")
        logger.info("Voice input system: Placeholder mode (LiveKit not implemented)")
        logger.info("Ready for development and testing")


async def main():
    """Main application entry point"""
    try:
        # Load configuration first to get debug mode
        settings = load_settings()

        # Setup logging with debug mode from settings
        debug_mode = settings.app.get("debug", False)
        logging_config = settings.monitoring.get("logging", {})

        setup_logging(
            level=logging_config.get("level", "INFO"),
            log_format=logging_config.get("format", "json"),
            log_file=logging_config.get("file", "logs/lumina.log"),
            max_file_size=logging_config.get("max_file_size", "10MB"),
            backup_count=logging_config.get("backup_count", 5),
            debug_mode=debug_mode,
        )

        logger = logging.getLogger(__name__)

        if debug_mode:
            logger.info("Starting Lumina AI in DEBUG mode...")
        else:
            logger.info("Starting Lumina AI...")

        # Initialize and start the application
        app = LuminaApp(settings)
        await app.initialize()

        logger.info("Lumina AI started successfully")

        # Display startup greeting and instructions
        await _display_startup_greeting(logger, debug_mode)

        # Play automatic voice greeting (Task 1: Automatic Voice Greeting)
        if not debug_mode:
            logger.info("🎵 Playing automatic voice greeting...")
            greeting_success = await app.play_startup_greeting()
            if greeting_success:
                logger.info("✅ Voice greeting completed")
            else:
                logger.info("ℹ️ Voice greeting skipped (audio system not available)")

        # Run the application
        await app.run()  # Production mode (runs indefinitely until Ctrl+C)

    except KeyboardInterrupt:
        logger.info("Received shutdown signal")
    except (ImportError, ModuleNotFoundError) as e:
        logger.error(f"Missing dependency: {e}")
        sys.exit(1)
    except (ValueError, TypeError, AttributeError) as e:
        logger.error(f"Configuration or initialization error: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
    finally:
        if "app" in locals():
            await app.cleanup()
        logger.info("Lumina AI shutdown complete")


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())
