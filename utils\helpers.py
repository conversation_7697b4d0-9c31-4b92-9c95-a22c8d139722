"""
Helper Utilities for Lumina AI

Common utility functions used across the application.
"""

import asyncio
import hashlib
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union
import json

from utils.logging import get_logger

logger = get_logger(__name__)


def generate_session_id() -> str:
    """Generate a unique session ID"""
    return str(uuid.uuid4())


def generate_request_id() -> str:
    """Generate a unique request ID"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    unique_id = str(uuid.uuid4())[:8]
    return f"req_{timestamp}_{unique_id}"


def hash_string(text: str) -> str:
    """Generate MD5 hash of a string"""
    return hashlib.md5(text.encode()).hexdigest()


def format_timestamp(dt: datetime = None) -> str:
    """Format datetime as ISO string"""
    if dt is None:
        dt = datetime.now()
    return dt.isoformat()


def parse_timestamp(timestamp_str: str) -> Optional[datetime]:
    """Parse ISO timestamp string"""
    try:
        return datetime.fromisoformat(timestamp_str)
    except (ValueError, TypeError):
        return None


def safe_json_loads(json_str: str, default: Any = None) -> Any:
    """Safely parse JSON string"""
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError):
        return default


def safe_json_dumps(obj: Any, default: str = "{}") -> str:
    """Safely serialize object to JSON"""
    try:
        return json.dumps(obj, ensure_ascii=False, indent=2)
    except (TypeError, ValueError):
        return default


def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """Truncate text to specified length"""
    if len(text) <= max_length:
        return text
    return text[: max_length - len(suffix)] + suffix


def extract_keywords(text: str, language: str = "en") -> List[str]:
    """
    Extract keywords from text

    Args:
        text: Input text
        language: Language code

    Returns:
        List of keywords
    """
    # Simple keyword extraction - split by spaces and filter
    words = text.lower().split()

    # Common stop words to filter out
    stop_words = {
        "en": {
            "the",
            "a",
            "an",
            "and",
            "or",
            "but",
            "in",
            "on",
            "at",
            "to",
            "for",
            "of",
            "with",
            "by",
            "is",
            "are",
            "was",
            "were",
            "be",
            "been",
            "have",
            "has",
            "had",
            "do",
            "does",
            "did",
            "will",
            "would",
            "could",
            "should",
            "may",
            "might",
            "can",
            "this",
            "that",
            "these",
            "those",
        },
        "hi": {
            "और",
            "या",
            "में",
            "पर",
            "से",
            "को",
            "का",
            "की",
            "के",
            "है",
            "हैं",
            "था",
            "थी",
            "थे",
            "हो",
            "होना",
            "करना",
            "कर",
            "किया",
            "यह",
            "वह",
            "इस",
            "उस",
        },
    }

    # Filter out stop words and short words
    language_stop_words = stop_words.get(language, stop_words["en"])
    keywords = [
        word for word in words if len(word) > 2 and word not in language_stop_words
    ]

    return keywords[:10]  # Return top 10 keywords


def detect_language_simple(text: str) -> str:
    """
    Simple language detection based on character sets

    Args:
        text: Input text

    Returns:
        Detected language code
    """
    import re

    # Check for Devanagari script (Hindi, Marathi, etc.)
    if re.search(r"[\u0900-\u097F]", text):
        return "hi"

    # Check for Gujarati script
    elif re.search(r"[\u0A80-\u0AFF]", text):
        return "gu"

    # Check for Tamil script
    elif re.search(r"[\u0B80-\u0BFF]", text):
        return "ta"

    # Check for Telugu script
    elif re.search(r"[\u0C00-\u0C7F]", text):
        return "te"

    # Check for Malayalam script
    elif re.search(r"[\u0D00-\u0D7F]", text):
        return "ml"

    # Check for Kannada script
    elif re.search(r"[\u0C80-\u0CFF]", text):
        return "kn"

    # Check for Bengali script
    elif re.search(r"[\u0980-\u09FF]", text):
        return "bn"

    # Default to English for Latin script
    else:
        return "en"


def calculate_jaccard_similarity(set1: set, set2: set) -> float:
    """
    Calculate Jaccard similarity between two sets

    Args:
        set1: First set
        set2: Second set

    Returns:
        Jaccard similarity score (0.0 to 1.0)
    """
    if not set1 and not set2:
        return 1.0

    intersection = len(set1.intersection(set2))
    union = len(set1.union(set2))

    return intersection / union if union > 0 else 0.0


def normalize_text(text: str) -> str:
    """
    Normalize text for processing

    Args:
        text: Input text

    Returns:
        Normalized text
    """
    import re

    # Convert to lowercase
    text = text.lower()

    # Remove extra whitespace
    text = re.sub(r"\s+", " ", text)

    # Remove special characters (keep alphanumeric and spaces)
    text = re.sub(r"[^\w\s]", "", text)

    return text.strip()


def chunk_text(text: str, chunk_size: int = 1000, overlap: int = 100) -> List[str]:
    """
    Split text into overlapping chunks

    Args:
        text: Input text
        chunk_size: Size of each chunk
        overlap: Overlap between chunks

    Returns:
        List of text chunks
    """
    if len(text) <= chunk_size:
        return [text]

    chunks = []
    start = 0

    while start < len(text):
        end = start + chunk_size
        chunk = text[start:end]
        chunks.append(chunk)

        if end >= len(text):
            break

        start = end - overlap

    return chunks
