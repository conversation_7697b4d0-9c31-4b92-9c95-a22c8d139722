# 🎯 Lumina AI Usage Guide

## 🚀 **IMPORTANT: Your Application is Working Correctly!**

If you see the application "stuck" after this message:
```
{"timestamp": "...", "level": "INFO", "logger": "utils.metrics", "message": "Starting metrics collection", ...}
```

**This is NORMAL behavior!** Your Lumina AI is running as a **long-running service** waiting for voice interactions.

---

## 📋 **Running Modes**

### 🔄 **Production Mode (Recommended for actual use)**
```bash
python main.py
```
- ✅ Runs indefinitely as a voice assistant service
- 🎤 Continuously listens for voice input
- 🤖 Processes LLM requests in real-time
- 🗣️ Generates voice responses
- 📊 Collects metrics
- ⏹️ **To stop: Press `Ctrl+C`**

### 🧪 **Test Mode (For verification)**
```bash
python main_test.py
```
- ✅ Runs for 10 seconds and exits automatically
- ✅ Proves all components work correctly
- ✅ Shows initialization success
- ✅ Perfect for testing configuration

### ⚡ **Quick Test with Timeout**
```bash
# Modify main.py temporarily to use:
await app.run_with_timeout(30)  # Runs for 30 seconds
```

---

## 🔍 **What Each Mode Does**

### **Production Mode Flow:**
1. ✅ Load environment variables from `.env`
2. ✅ Initialize all components (storage, LLM, agents, voice)
3. ✅ Start voice handler and metrics collection
4. 🔄 **Enter infinite loop waiting for voice interactions**
5. ⏹️ **Stays running until you press Ctrl+C**

### **Test Mode Flow:**
1. ✅ Same initialization as production
2. ✅ Run for specified duration (10 seconds)
3. ✅ Show countdown and status
4. ✅ Clean shutdown automatically
5. ✅ Confirm everything works

---

## 🎯 **Verification Commands**

### **Check Environment Variables:**
```bash
python test_env_vars.py
```
Expected output: `✅ Loaded: 13, ❌ Missing: 0, 📈 Success Rate: 100.0%`

### **Full System Demo:**
```bash
python demo_lumina.py
```
Shows complete system status and capabilities.

### **Quick Test:**
```bash
python main_test.py
```
Runs for 10 seconds and confirms everything works.

---

## 🛠️ **Troubleshooting**

### **If Application Appears "Stuck":**
1. ✅ **This is normal!** The app is running as a service
2. ✅ Check logs - you should see "Starting metrics collection"
3. ✅ Press `Ctrl+C` to stop when ready
4. ✅ Use `python main_test.py` for quick verification

### **If You See Errors:**
1. 🔍 Check environment variables: `python test_env_vars.py`
2. 🔍 Verify API keys in `.env` file
3. 🔍 Check network connectivity for LLM providers
4. 🔍 Review logs for specific error messages

### **Common Issues:**
- **"Environment variable not found"**: Run `python test_env_vars.py` to verify
- **"API key not configured"**: Check `.env` file format and values
- **"Failed to initialize"**: Check network and API key validity

---

## 📊 **Expected Log Messages**

### **Successful Startup:**
```
✅ "Logging configured successfully"
✅ "Loaded environment variables from D:\DevGen\Lumina\.env"
✅ "Gemini client initialized successfully"
✅ "Groq client initialized successfully"
✅ "LLM Router initialized with 2 healthy providers"
✅ "All components initialized successfully"
✅ "Lumina AI started successfully"
✅ "Starting main application loop..."
✅ "Starting metrics collection"
```

### **After This - Application Runs Continuously:**
The application is now **actively running** and ready for voice interactions!

---

## 🎉 **Success Indicators**

Your Lumina AI is working correctly if you see:
- ✅ All environment variables loaded (100% success rate)
- ✅ Both Gemini and Groq providers initialized
- ✅ No error messages during startup
- ✅ "Starting metrics collection" message appears
- ✅ Application stays running (doesn't crash or exit)

---

## 🚀 **Next Steps**

1. **For Testing**: Use `python main_test.py` to verify functionality
2. **For Development**: Use `python main.py` and press `Ctrl+C` when done
3. **For Production**: Use `python main.py` and let it run continuously
4. **For Monitoring**: Check logs and metrics for system health

Your Lumina AI voice assistant is now **fully operational** and ready for multilingual voice conversations! 🎉
