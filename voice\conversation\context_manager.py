"""
Conversation Context Manager for Lumina AI
Manages conversation history, context, and session state for voice interactions
"""

import asyncio
import logging
import time
import uuid
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum

from config.settings import get_settings
from utils.logging import get_logger

logger = get_logger(__name__)


class ConversationState(Enum):
    """States of a conversation session"""
    IDLE = "idle"
    LISTENING = "listening"
    PROCESSING = "processing"
    RESPONDING = "responding"
    WAITING_FOR_RESPONSE = "waiting_for_response"
    ENDED = "ended"


@dataclass
class ConversationTurn:
    """Represents a single turn in a conversation"""
    turn_id: str
    timestamp: datetime
    user_input: str
    user_language: str
    ai_response: str
    response_language: str
    processing_time: float
    confidence: float
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ConversationSession:
    """Represents a complete conversation session"""
    session_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    state: ConversationState = ConversationState.IDLE
    turns: List[ConversationTurn] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)
    user_preferences: Dict[str, Any] = field(default_factory=dict)
    total_turns: int = 0
    primary_language: str = "hi"
    
    @property
    def duration(self) -> float:
        """Get session duration in seconds"""
        end = self.end_time or datetime.now()
        return (end - self.start_time).total_seconds()
    
    @property
    def is_active(self) -> bool:
        """Check if session is currently active"""
        return self.state not in [ConversationState.IDLE, ConversationState.ENDED]


class ConversationContextManager:
    """Manages conversation context and session state"""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = logging.getLogger(__name__)
        
        # Session management
        self.current_session: Optional[ConversationSession] = None
        self.session_history: Dict[str, ConversationSession] = {}
        
        # Configuration
        self.max_session_duration = self.settings.app.get("max_session_duration", 86400)  # 24 hours
        self.max_context_turns = 10  # Keep last 10 turns for context
        self.session_timeout = 300  # 5 minutes of inactivity
        
        # Context tracking
        self.conversation_topics: List[str] = []
        self.user_preferences: Dict[str, Any] = {}
        self.session_metrics: Dict[str, Any] = {}
        
        # Language and personalization
        self.default_language = self.settings.app.get("default_language", "hi")
        self.supported_languages = self.settings.app.get("supported_languages", ["hi", "en"])
        
    async def start_session(self, user_id: Optional[str] = None) -> str:
        """Start a new conversation session"""
        try:
            session_id = str(uuid.uuid4())
            
            self.current_session = ConversationSession(
                session_id=session_id,
                start_time=datetime.now(),
                state=ConversationState.LISTENING,
                primary_language=self.default_language,
                user_preferences=self.user_preferences.copy()
            )
            
            self.session_history[session_id] = self.current_session
            
            self.logger.info(f"🎯 Started new conversation session: {session_id}")
            return session_id
            
        except Exception as e:
            self.logger.error(f"Failed to start conversation session: {e}")
            raise
    
    async def end_session(self, session_id: Optional[str] = None) -> bool:
        """End a conversation session"""
        try:
            session = self.current_session if session_id is None else self.session_history.get(session_id)
            
            if not session:
                self.logger.warning(f"No session found to end: {session_id}")
                return False
            
            session.end_time = datetime.now()
            session.state = ConversationState.ENDED
            
            # Log session summary
            self.logger.info(
                f"🎯 Ended conversation session {session.session_id}: "
                f"{session.total_turns} turns, {session.duration:.1f}s duration"
            )
            
            # Clear current session if it's the one being ended
            if self.current_session and self.current_session.session_id == session.session_id:
                self.current_session = None
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to end conversation session: {e}")
            return False
    
    async def add_conversation_turn(
        self,
        user_input: str,
        ai_response: str,
        user_language: str = None,
        response_language: str = None,
        confidence: float = 1.0,
        processing_time: float = 0.0,
        metadata: Dict[str, Any] = None
    ) -> str:
        """Add a conversation turn to the current session"""
        try:
            if not self.current_session:
                await self.start_session()
            
            turn_id = str(uuid.uuid4())
            
            turn = ConversationTurn(
                turn_id=turn_id,
                timestamp=datetime.now(),
                user_input=user_input,
                user_language=user_language or self.default_language,
                ai_response=ai_response,
                response_language=response_language or self.default_language,
                processing_time=processing_time,
                confidence=confidence,
                metadata=metadata or {}
            )
            
            self.current_session.turns.append(turn)
            self.current_session.total_turns += 1
            
            # Update context with recent turns (keep only last N turns)
            if len(self.current_session.turns) > self.max_context_turns:
                self.current_session.turns = self.current_session.turns[-self.max_context_turns:]
            
            self.logger.info(
                f"💬 Added conversation turn {turn_id}: '{user_input[:50]}...' -> '{ai_response[:50]}...'"
            )
            
            return turn_id
            
        except Exception as e:
            self.logger.error(f"Failed to add conversation turn: {e}")
            raise
    
    def get_conversation_context(self, max_turns: int = 5) -> List[Dict[str, Any]]:
        """Get recent conversation context for LLM processing"""
        try:
            if not self.current_session or not self.current_session.turns:
                return []
            
            recent_turns = self.current_session.turns[-max_turns:]
            
            context = []
            for turn in recent_turns:
                context.append({
                    "role": "user",
                    "content": turn.user_input,
                    "language": turn.user_language,
                    "timestamp": turn.timestamp.isoformat()
                })
                context.append({
                    "role": "assistant",
                    "content": turn.ai_response,
                    "language": turn.response_language,
                    "timestamp": turn.timestamp.isoformat()
                })
            
            return context
            
        except Exception as e:
            self.logger.error(f"Failed to get conversation context: {e}")
            return []
    
    def update_session_state(self, state: ConversationState):
        """Update the current session state"""
        if self.current_session:
            self.current_session.state = state
            self.logger.debug(f"🎯 Session state updated to: {state.value}")
    
    def get_session_summary(self, session_id: Optional[str] = None) -> Dict[str, Any]:
        """Get a summary of the session"""
        try:
            session = self.current_session if session_id is None else self.session_history.get(session_id)
            
            if not session:
                return {}
            
            return {
                "session_id": session.session_id,
                "start_time": session.start_time.isoformat(),
                "end_time": session.end_time.isoformat() if session.end_time else None,
                "duration": session.duration,
                "state": session.state.value,
                "total_turns": session.total_turns,
                "primary_language": session.primary_language,
                "is_active": session.is_active,
                "recent_topics": self.conversation_topics[-5:] if self.conversation_topics else []
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get session summary: {e}")
            return {}
    
    async def cleanup_old_sessions(self):
        """Clean up old inactive sessions"""
        try:
            current_time = datetime.now()
            sessions_to_remove = []
            
            for session_id, session in self.session_history.items():
                # Remove sessions older than max duration or inactive for too long
                if (session.duration > self.max_session_duration or 
                    (current_time - session.start_time).total_seconds() > self.session_timeout):
                    
                    if session.state != ConversationState.ENDED:
                        await self.end_session(session_id)
                    
                    sessions_to_remove.append(session_id)
            
            # Remove old sessions from history
            for session_id in sessions_to_remove:
                del self.session_history[session_id]
                
            if sessions_to_remove:
                self.logger.info(f"🧹 Cleaned up {len(sessions_to_remove)} old sessions")
                
        except Exception as e:
            self.logger.error(f"Failed to cleanup old sessions: {e}")
    
    def extract_topics_from_text(self, text: str) -> List[str]:
        """Extract conversation topics from text (simple keyword-based approach)"""
        try:
            # Simple topic extraction - can be enhanced with NLP
            common_topics = [
                "weather", "time", "date", "music", "news", "sports", "food", 
                "travel", "work", "family", "health", "technology", "movies"
            ]
            
            text_lower = text.lower()
            found_topics = [topic for topic in common_topics if topic in text_lower]
            
            # Add to conversation topics
            for topic in found_topics:
                if topic not in self.conversation_topics:
                    self.conversation_topics.append(topic)
            
            return found_topics
            
        except Exception as e:
            self.logger.error(f"Failed to extract topics: {e}")
            return []
    
    def get_status(self) -> Dict[str, Any]:
        """Get context manager status"""
        return {
            "current_session": self.get_session_summary() if self.current_session else None,
            "total_sessions": len(self.session_history),
            "active_sessions": sum(1 for s in self.session_history.values() if s.is_active),
            "conversation_topics": self.conversation_topics[-10:],  # Last 10 topics
            "user_preferences": self.user_preferences,
            "default_language": self.default_language,
            "supported_languages": self.supported_languages
        }
