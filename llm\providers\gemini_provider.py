"""
Google Gemini LLM Provider for Lumina AI

Integrates with Google's Gemini API for natural language processing.
"""

import asyncio
import json
import os
from typing import Dict, List, Optional, Any
from datetime import datetime

import google.generativeai as genai
from google.generativeai.types import <PERSON>rm<PERSON>ategory, <PERSON>rmBlockThreshold

from llm.providers.base_provider import BaseLLMProvider, LLMRequest, LLMResponse
from config.settings import get_settings
from utils.logging import get_logger

logger = get_logger(__name__)


class GeminiProvider(BaseLLMProvider):
    """
    Google Gemini LLM Provider implementation
    """

    def __init__(self, config: Dict[str, Any] = None):
        self.settings = get_settings()
        config = config or self.settings.llm.get("gemini", {})
        super().__init__("gemini", config)

        self.api_key = config.get("api_key") or os.getenv("GOOGLE_API_KEY")
        self.model_name = config.get("model", "gemini-1.5-flash")
        self.max_tokens = config.get("max_tokens", 8192)
        self.temperature = config.get("temperature", 0.7)
        self.model = None

    async def _initialize_client(self) -> None:
        """Initialize Gemini client"""
        if self.api_key:
            genai.configure(api_key=self.api_key)
            self.model = genai.GenerativeModel(self.model_name)
            logger.info("Gemini client initialized successfully")
        else:
            logger.warning("Gemini API key not configured")
            raise ValueError("GOOGLE_API_KEY environment variable not set")

    async def generate(self, request: LLMRequest) -> LLMResponse:
        """Generate response using Gemini API"""
        if not self.model:
            return LLMResponse(
                content="Gemini provider not configured",
                success=False,
                error="API key not available",
                provider="gemini",
                model=self.model_name,
                timestamp=datetime.now(),
            )

        try:
            # Prepare the prompt
            prompt = self._build_prompt(request)

            # Configure generation parameters
            generation_config = genai.types.GenerationConfig(
                temperature=self.temperature,
                max_output_tokens=self.max_tokens,
                candidate_count=1,
            )

            # Safety settings for Hindi content
            safety_settings = {
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: (
                    HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
                ),
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: (
                    HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
                ),
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: (
                    HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
                ),
                HarmCategory.HARM_CATEGORY_HARASSMENT: (
                    HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
                ),
            }

            # Generate response
            response = await asyncio.to_thread(
                self.model.generate_content,
                prompt,
                generation_config=generation_config,
                safety_settings=safety_settings,
            )

            if response.candidates and response.candidates[0].content:
                content = response.candidates[0].content.parts[0].text

                return LLMResponse(
                    content=content.strip(),
                    success=True,
                    provider="gemini",
                    model=self.model_name,
                    timestamp=datetime.now(),
                    metadata={
                        "finish_reason": (
                            response.candidates[0].finish_reason.name
                            if response.candidates[0].finish_reason
                            else None
                        ),
                        "safety_ratings": (
                            [
                                {
                                    "category": rating.category.name,
                                    "probability": rating.probability.name,
                                }
                                for rating in response.candidates[0].safety_ratings
                            ]
                            if response.candidates[0].safety_ratings
                            else []
                        ),
                        "usage": (
                            {
                                "prompt_tokens": (
                                    response.usage_metadata.prompt_token_count
                                    if response.usage_metadata
                                    else 0
                                ),
                                "completion_tokens": (
                                    response.usage_metadata.candidates_token_count
                                    if response.usage_metadata
                                    else 0
                                ),
                                "total_tokens": (
                                    response.usage_metadata.total_token_count
                                    if response.usage_metadata
                                    else 0
                                ),
                            }
                            if response.usage_metadata
                            else {}
                        ),
                    },
                )

            return LLMResponse(
                content="No response generated",
                success=False,
                error="Empty response from Gemini",
                provider="gemini",
                model=self.model_name,
                timestamp=datetime.now(),
            )

        except Exception as e:
            logger.error("Gemini API error: %s", e)
            return LLMResponse(
                content="",
                success=False,
                error=str(e),
                provider="gemini",
                model=self.model_name,
                timestamp=datetime.now(),
            )

    def _build_prompt(self, request: LLMRequest) -> str:
        """Build the prompt for Gemini"""
        prompt_parts = []

        # Add system prompt
        if request.system_prompt:
            prompt_parts.append(f"System: {request.system_prompt}")

        # Add conversation history
        if request.conversation_history:
            prompt_parts.append("Conversation History:")
            for turn in request.conversation_history:
                prompt_parts.append(f"User: {turn.user_input}")
                prompt_parts.append(f"Assistant: {turn.agent_response}")

        # Add current user input
        prompt_parts.append(f"User: {request.user_input}")
        prompt_parts.append("Assistant:")

        return "\n".join(prompt_parts)

    async def check_health(self) -> bool:
        """Check if Gemini API is accessible"""
        if not self.model:
            return False

        try:
            # Simple health check with minimal request
            test_request = LLMRequest(
                user_input="Hello", language="en", session_id="health_check"
            )

            response = await self.generate_response(test_request)
            return response.success

        except Exception as e:
            logger.error("Gemini health check failed: %s", e)
            return False

    def is_available(self) -> bool:
        """Check if Gemini provider is available"""
        return self._initialized and self.model is not None

    def get_provider_info(self) -> Dict[str, Any]:
        """Get provider information"""
        return {
            "name": "gemini",
            "model": self.model_name,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "configured": self.model is not None,
            "supports_streaming": False,
            "supports_function_calling": True,
            "supported_languages": [
                "hi",
                "en",
                "mr",
                "gu",
                "ta",
                "te",
                "bn",
                "kn",
                "ml",
                "or",
                "pa",
                "as",
            ],
        }

    async def estimate_tokens(self, text: str) -> int:
        """Estimate token count for text"""
        if not self.model:
            # Rough estimation: 1 token ≈ 4 characters for most languages
            return len(text) // 4

        try:
            # Use Gemini's token counting if available
            response = await asyncio.to_thread(self.model.count_tokens, text)
            return response.total_tokens
        except Exception:
            # Fallback to rough estimation
            return len(text) // 4

    def get_rate_limits(self) -> Dict[str, Any]:
        """Get rate limit information"""
        return {
            "requests_per_minute": 60,
            "tokens_per_minute": 32000,
            "requests_per_day": 1500,
        }
