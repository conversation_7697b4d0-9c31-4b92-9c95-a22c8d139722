"""
OpenAI Whisper Speech-to-Text implementation for Lumina AI
"""

import asyncio
import io
import tempfile
import os
import wave
from typing import Optional, Dict, Any, List, AsyncIterator
from datetime import datetime

try:
    import whisper
    import torch
    import numpy as np

    WHISPER_AVAILABLE = True
except ImportError:
    WHISPER_AVAILABLE = False

from voice.base.stt_interface import STTInterface, STTResult
from config.settings import get_settings
from utils.logging import get_logger

logger = get_logger(__name__)


class WhisperSTT(STTInterface):
    """
    OpenAI Whisper Speech-to-Text implementation
    """

    def __init__(self):
        super().__init__()
        self.settings = get_settings()
        self.model = None
        self.model_name = self.settings.voice.get("stt", {}).get(
            "whisper_model", "base"
        )
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.supported_languages = [
            "hi",
            "en",
            "mr",
            "gu",
            "ta",
            "te",
            "bn",
            "kn",
            "ml",
            "pa",
            "or",
            "as",
        ]

        # Whisper model configurations
        self.model_configs = {
            "tiny": {"size": "39MB", "speed": "fast", "accuracy": "low"},
            "base": {"size": "74MB", "speed": "medium", "accuracy": "medium"},
            "small": {"size": "244MB", "speed": "medium", "accuracy": "good"},
            "medium": {"size": "769MB", "speed": "slow", "accuracy": "high"},
            "large": {"size": "1550MB", "speed": "very_slow", "accuracy": "very_high"},
        }

    async def initialize(self) -> bool:
        """Initialize Whisper STT"""
        logger.info("Initializing Whisper STT")

        if not WHISPER_AVAILABLE:
            logger.error(
                "❌ Whisper dependencies not available. Install with: pip install openai-whisper torch"
            )
            return False

        try:
            # Check if we can access the temporary directory
            temp_dir = tempfile.gettempdir()
            logger.debug(f"Temporary directory: {temp_dir}")

            if not os.access(temp_dir, os.W_OK):
                logger.error(f"❌ Cannot write to temporary directory: {temp_dir}")
                return False

            # Load Whisper model
            logger.info(f"Loading Whisper model: {self.model_name}")
            logger.info("⏳ This may take a few moments on first run...")

            self.model = whisper.load_model(self.model_name, device=self.device)

            logger.info(f"✅ Whisper STT initialized successfully on {self.device}")
            logger.info(
                f"📊 Model: {self.model_name} ({self.model_configs.get(self.model_name, {}).get('size', 'unknown size')})"
            )

            return True

        except Exception as e:
            logger.error(f"❌ Failed to initialize Whisper STT: {str(e)}")
            logger.error(
                "💡 Try installing dependencies: pip install openai-whisper torch"
            )
            return False

    async def transcribe_audio(
        self, audio_data: bytes, language: Optional[str] = None
    ) -> STTResult:
        """
        Transcribe audio using Whisper

        Args:
            audio_data: Raw audio bytes
            language: Target language code (optional)

        Returns:
            STTResult with transcription
        """
        start_time = datetime.now()

        try:
            if not self.model:
                return STTResult(
                    text="",
                    confidence=0.0,
                    language="",
                    processing_time=0.0,
                    success=False,
                    error="Whisper model not initialized",
                )

            # Validate audio data
            if not audio_data or len(audio_data) == 0:
                return STTResult(
                    text="",
                    confidence=0.0,
                    language="",
                    processing_time=0.0,
                    success=False,
                    error="No audio data provided",
                )

            # Save audio data to temporary WAV file with proper headers
            temp_file_path = None
            try:
                # Create temporary file in system temp directory with proper permissions
                temp_dir = tempfile.gettempdir()
                logger.debug(f"Using temporary directory: {temp_dir}")

                with tempfile.NamedTemporaryFile(
                    suffix=".wav", delete=False, dir=temp_dir
                ) as temp_file:
                    temp_file_path = temp_file.name
                    logger.debug(f"Created temporary file: {temp_file_path}")

                # Create proper WAV file with headers
                with wave.open(temp_file_path, "wb") as wav_file:
                    # Set WAV parameters (matching microphone configuration)
                    wav_file.setnchannels(1)  # Mono
                    wav_file.setsampwidth(2)  # 16-bit (2 bytes per sample)
                    wav_file.setframerate(16000)  # 16kHz sample rate
                    wav_file.writeframes(audio_data)

                logger.debug(f"WAV file created successfully: {temp_file_path}")

            except Exception as e:
                logger.error(f"Error creating WAV file: {e}")
                if temp_file_path and os.path.exists(temp_file_path):
                    try:
                        os.unlink(temp_file_path)
                    except Exception as cleanup_error:
                        logger.warning(f"Failed to cleanup temp file: {cleanup_error}")

                return STTResult(
                    text="",
                    confidence=0.0,
                    language="",
                    processing_time=(datetime.now() - start_time).total_seconds(),
                    success=False,
                    error=f"Failed to create temporary audio file: {str(e)}",
                )

            try:
                # Verify the WAV file was created successfully
                if not os.path.exists(temp_file_path):
                    raise FileNotFoundError(
                        f"Temporary WAV file not created: {temp_file_path}"
                    )

                # Check file size
                file_size = os.path.getsize(temp_file_path)
                if file_size == 0:
                    raise ValueError(f"Temporary WAV file is empty: {temp_file_path}")

                logger.debug(f"WAV file verified: {temp_file_path} ({file_size} bytes)")

                # Transcribe with Whisper
                # Enhanced language detection: let Whisper auto-detect if language not specified or not supported
                whisper_language = None
                if language and language in self.supported_languages:
                    whisper_language = language
                    logger.debug(f"Using specified language: {language}")
                else:
                    logger.debug("Using automatic language detection")

                logger.debug(
                    f"Starting Whisper transcription for file: {temp_file_path}"
                )

                try:
                    result = self.model.transcribe(
                        temp_file_path,
                        language=whisper_language,
                        task="transcribe",
                        fp16=False if self.device == "cpu" else True,
                        verbose=False,  # Reduce verbose output
                    )
                    logger.debug("Whisper transcription completed successfully")

                except Exception as whisper_error:
                    logger.error(f"Whisper transcription failed: {whisper_error}")
                    raise Exception(
                        f"Whisper model transcription error: {str(whisper_error)}"
                    )

                # Extract results
                text = result["text"].strip()
                detected_language = result.get("language", language or "unknown")

                # Enhanced language detection logging
                if language and language != detected_language:
                    logger.info(
                        f"Language mismatch: requested={language}, detected={detected_language}"
                    )

                # Validate detected language is supported
                if detected_language not in self.supported_languages:
                    logger.warning(
                        f"Detected language '{detected_language}' not in supported languages. Using 'en' as fallback."
                    )
                    detected_language = "en"  # Fallback to English

                # Calculate confidence (Whisper doesn't provide direct confidence)
                # Use segment-level confidence if available
                confidence = self._calculate_confidence(result)

                processing_time = (datetime.now() - start_time).total_seconds()

                logger.info(
                    f"STT completed: '{text[:50]}...' (lang: {detected_language}, conf: {confidence:.2f})"
                )

                return STTResult(
                    text=text,
                    confidence=confidence,
                    language=detected_language,
                    processing_time=processing_time,
                    success=True,
                    metadata={
                        "model": self.model_name,
                        "device": self.device,
                        "segments": len(result.get("segments", [])),
                        "duration": result.get("duration", 0.0),
                    },
                )

            finally:
                # Clean up temporary file
                if temp_file_path:
                    try:
                        if os.path.exists(temp_file_path):
                            os.unlink(temp_file_path)
                            logger.debug(f"Cleaned up temporary file: {temp_file_path}")
                    except Exception as cleanup_error:
                        logger.warning(
                            f"Failed to cleanup temporary file {temp_file_path}: {cleanup_error}"
                        )

        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"❌ ❌ Error during transcription: {str(e)}")

            return STTResult(
                text="",
                confidence=0.0,
                language="",
                processing_time=processing_time,
                success=False,
                error=str(e),
            )

    async def transcribe_stream(self, audio_stream) -> AsyncIterator[STTResult]:
        """
        Transcribe streaming audio (not implemented for Whisper)
        Whisper is designed for complete audio files, not streaming
        """
        logger.warning("Streaming transcription not supported by Whisper")
        yield STTResult(
            text="",
            confidence=0.0,
            language="",
            processing_time=0.0,
            success=False,
            error="Streaming not supported by Whisper",
        )

    def _calculate_confidence(self, whisper_result: Dict[str, Any]) -> float:
        """
        Calculate confidence score from Whisper result

        Args:
            whisper_result: Whisper transcription result

        Returns:
            Confidence score between 0.0 and 1.0
        """
        try:
            segments = whisper_result.get("segments", [])

            if not segments:
                return 0.5  # Default confidence if no segments

            # Calculate average confidence from segments
            total_confidence = 0.0
            total_duration = 0.0

            for segment in segments:
                # Whisper provides average log probability
                avg_logprob = segment.get("avg_logprob", -1.0)
                duration = segment.get("end", 0) - segment.get("start", 0)

                # Convert log probability to confidence (rough approximation)
                # avg_logprob ranges from -inf to 0, where 0 is highest confidence
                segment_confidence = max(0.0, min(1.0, (avg_logprob + 1.0)))

                total_confidence += segment_confidence * duration
                total_duration += duration

            if total_duration > 0:
                return total_confidence / total_duration
            else:
                return 0.5

        except Exception as e:
            logger.warning(f"Error calculating confidence: {str(e)}")
            return 0.5

    def get_supported_languages(self) -> List[str]:
        """Get list of supported language codes"""
        return self.supported_languages.copy()

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model"""
        return {
            "name": "OpenAI Whisper",
            "model": self.model_name,
            "device": self.device,
            "config": self.model_configs.get(self.model_name, {}),
            "supported_languages": self.supported_languages,
            "streaming_support": False,
            "multilingual": True,
        }

    async def cleanup(self) -> None:
        """Cleanup resources"""
        try:
            if self.model:
                # Clear model from memory
                del self.model
                self.model = None

                # Clear CUDA cache if using GPU
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

            logger.info("Whisper STT cleanup completed")

        except Exception as e:
            logger.error(f"Error during Whisper STT cleanup: {str(e)}")

    def is_available(self) -> bool:
        """Check if Whisper is available"""
        return WHISPER_AVAILABLE and self.model is not None

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check"""
        return {
            "service": "Whisper STT",
            "status": "healthy" if self.is_available() else "unhealthy",
            "model": self.model_name,
            "device": self.device,
            "dependencies_available": WHISPER_AVAILABLE,
            "model_loaded": self.model is not None,
            "supported_languages": len(self.supported_languages),
        }
