"""
Agent Router for Lumina AI

Routes user requests to appropriate agents based on intent analysis.
"""

import asyncio
import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

from agents.base.agent_interface import BaseAgent, AgentRequest, AgentResponse
from agents.base.agent_registry import AgentRegistry
from config.settings import get_settings
from utils.logging import get_logger
from utils.helpers import extract_keywords, detect_language_simple

logger = get_logger(__name__)


class AgentRouter:
    """
    Routes requests to appropriate agents based on intent analysis
    """

    def __init__(self, agent_registry: Optional[AgentRegistry] = None):
        self.settings = get_settings()
        self.agent_registry = agent_registry or AgentRegistry()
        self.agents: Dict[str, BaseAgent] = {}
        self.agent_keywords: Dict[str, Dict[str, List[str]]] = {}
        self.confidence_threshold = 0.3
        self.default_agent = "general"

        # Load agent configurations
        self.agent_configs = self.settings.agents

    async def initialize(self):
        """Initialize the agent router"""
        logger.info("Initializing Agent Router")

        # Initialize agent registry
        await self.agent_registry.initialize()

        # Load agent keyword mappings from configuration
        self._load_agent_keywords()

        logger.info(f"Agent router initialized with {len(self.agents)} agents")

    async def route_request(self, request: AgentRequest) -> AgentResponse:
        """Route a request to the appropriate agent"""
        try:
            # Analyze intent and find best agent
            best_agent, confidence = await self._analyze_intent(request)

            if not best_agent:
                return self._create_fallback_response(request)

            # Route to the selected agent
            logger.info(
                f"Routing request to agent: {best_agent} (confidence: {confidence:.2f})"
            )

            agent = self.agents.get(best_agent)
            if not agent:
                logger.error(f"Agent {best_agent} not found in registry")
                return self._create_fallback_response(request)

            # Process request with selected agent
            response = await agent.process_request(request)

            # Add routing metadata
            if response.metadata is None:
                response.metadata = {}
            response.metadata.update(
                {
                    "router_confidence": confidence,
                    "selected_agent": best_agent,
                    "routing_timestamp": datetime.now().isoformat(),
                }
            )

            return response

        except Exception as e:
            logger.error(f"Error routing request: {str(e)}")
            return AgentResponse(
                content=self._get_error_message(request.language),
                success=False,
                agent_name="router",
                timestamp=datetime.now(),
                error=str(e),
            )

    def register_agent(self, agent: BaseAgent) -> bool:
        """Register an agent with the router"""
        try:
            agent_name = agent.get_name()

            # Register with agent registry
            if not self.agent_registry.register_agent(agent):
                return False

            # Add to local registry
            self.agents[agent_name] = agent

            # Load keywords for this agent
            if hasattr(agent, "keywords"):
                self.agent_keywords[agent_name] = agent.keywords

            logger.info(f"Agent {agent_name} registered with router")
            return True

        except Exception as e:
            logger.error(f"Failed to register agent: {str(e)}")
            return False

    async def get_available_agents(self) -> List[str]:
        """Get list of available agents"""
        return list(self.agents.keys())

    async def _analyze_intent(
        self, request: AgentRequest
    ) -> Tuple[Optional[str], float]:
        """Analyze user intent and return best agent with confidence score"""
        user_input = request.user_input.lower()
        language = request.language

        # Extract keywords from user input
        keywords = extract_keywords(user_input, language)

        # Calculate confidence scores for each agent
        agent_scores = {}

        for agent_name, agent in self.agents.items():
            # Check if agent can handle the request
            if hasattr(agent, "can_handle_request"):
                confidence = agent.can_handle_request(request)
                if confidence > 0:
                    agent_scores[agent_name] = confidence

            # Also check keyword matching
            keyword_score = self._calculate_keyword_score(
                agent_name, keywords, language
            )
            if keyword_score > 0:
                # Combine scores (weighted average)
                existing_score = agent_scores.get(agent_name, 0)
                agent_scores[agent_name] = max(existing_score, keyword_score * 0.8)

        # Find best agent
        if not agent_scores:
            return None, 0.0

        best_agent = max(agent_scores, key=agent_scores.get)
        best_score = agent_scores[best_agent]

        # Check if confidence meets threshold
        if best_score < self.confidence_threshold:
            return None, best_score

        return best_agent, best_score

    def _calculate_keyword_score(
        self, agent_name: str, keywords: List[str], language: str
    ) -> float:
        """Calculate keyword matching score for an agent"""
        if agent_name not in self.agent_keywords:
            return 0.0

        agent_keywords = self.agent_keywords[agent_name].get(language, [])
        if not agent_keywords:
            # Fallback to English keywords
            agent_keywords = self.agent_keywords[agent_name].get("en", [])

        if not agent_keywords or not keywords:
            return 0.0

        # Calculate Jaccard similarity
        keyword_set = set(keywords)
        agent_keyword_set = set(agent_keywords)

        intersection = keyword_set.intersection(agent_keyword_set)
        union = keyword_set.union(agent_keyword_set)

        if not union:
            return 0.0

        return len(intersection) / len(union)

    def _load_agent_keywords(self):
        """Load agent keyword mappings from configuration"""
        # Default keyword mappings for built-in agents
        default_keywords = {
            "weather": {
                "hi": ["मौसम", "बारिश", "धूप", "ठंड", "गर्मी", "तापमान"],
                "en": ["weather", "temperature", "rain", "sunny", "cloudy", "forecast"],
                "mr": ["हवामान", "पाऊस", "सूर्य", "थंडी", "उष्णता"],
                "gu": ["હવામાન", "વરસાદ", "સૂર્ય", "ઠંડક", "ગરમી"],
                "ta": ["வானிலை", "மழை", "சூரியன்", "குளிர்", "வெப்பம்"],
                "te": ["వాతావరణం", "వర్షం", "సూర్యుడు", "చల్లదనం", "వేడిమి"],
            },
            "reminder": {
                "hi": ["याद", "रिमाइंडर", "समय", "अलार्म", "कार्य"],
                "en": [
                    "remind",
                    "reminder",
                    "schedule",
                    "alarm",
                    "task",
                    "appointment",
                ],
                "mr": ["आठवण", "रिमाइंडर", "वेळ", "अलार्म", "कार्य"],
                "gu": ["યાદ", "રિમાઇન્ડર", "સમય", "અલાર્મ", "કાર્ય"],
                "ta": ["நினைவூட்டல்", "நேரம்", "அலாரம்", "பணி"],
                "te": ["గుర్తుచేయు", "సమయం", "అలారం", "పని"],
            },
            "news": {
                "hi": ["समाचार", "न्यूज़", "खबर", "ताजा"],
                "en": ["news", "headlines", "current", "latest", "breaking"],
                "mr": ["बातमी", "न्यूज", "ताजी"],
                "gu": ["સમાચાર", "ન્યૂઝ", "તાજા"],
                "ta": ["செய்தி", "புதிய"],
                "te": ["వార్తలు", "కొత్త"],
            },
        }

        # Merge with configuration
        for agent_name, keywords in default_keywords.items():
            self.agent_keywords[agent_name] = keywords

    def _create_fallback_response(self, request: AgentRequest) -> AgentResponse:
        """Create fallback response when no suitable agent is found"""
        fallback_messages = {
            "hi": "मुझे समझ नहीं आया कि आप क्या चाहते हैं। कृपया अपना प्रश्न दोबारा पूछें।",
            "en": "I didn't understand what you're looking for. Please rephrase your question.",
            "mr": "मला समजले नाही की तुम्हाला काय हवे आहे. कृपया तुमचा प्रश्न पुन्हा विचारा.",
            "gu": "મને સમજાયું નથી કે તમને શું જોઈએ છે. કૃપા કરીને તમારો પ્રશ્ન ફરીથી પૂછો.",
            "ta": "நீங்கள் என்ன தேடுகிறீர்கள் என்று எனக்குப் புரியவில்லை. உங்கள் கேள்வியை மீண்டும் கேளுங்கள்.",
            "te": "మీరు ఏమి వెతుకుతున్నారో నాకు అర్థం కాలేదు. దయచేసి మీ ప్రశ్నను మళ్లీ అడగండి.",
        }

        content = fallback_messages.get(request.language, fallback_messages["en"])

        return AgentResponse(
            content=content,
            success=True,
            agent_name="router",
            timestamp=datetime.now(),
            metadata={"fallback": True, "available_agents": list(self.agents.keys())},
        )

    def _get_error_message(self, language: str) -> str:
        """Get error message in specified language"""
        error_messages = {
            "hi": "कुछ गलत हुआ है। कृपया बाद में कोशिश करें।",
            "en": "Something went wrong. Please try again later.",
            "mr": "काहीतरी चूक झाली आहे. कृपया नंतर प्रयत्न करा.",
            "gu": "કંઈક ખોટું થયું છે. કૃપા કરીને પછીથી પ્રયાસ કરો.",
            "ta": "ஏதோ தவறு நடந்துள்ளது. பின்னர் முயற்சிக்கவும்.",
            "te": "ఏదో తప్పు జరిగింది. దయచేసి తర్వాత ప్రయత్నించండి.",
        }

        return error_messages.get(language, error_messages["en"])

    async def cleanup(self):
        """Cleanup agent router resources"""
        try:
            logger.info("Cleaning up Agent Router")

            # Cleanup agent registry
            if hasattr(self.agent_registry, "cleanup"):
                await self.agent_registry.cleanup()

            # Clear agents
            self.agents.clear()
            self.agent_keywords.clear()

            logger.info("Agent Router cleanup completed")

        except Exception as e:
            logger.error(f"Error during Agent Router cleanup: {e}")
