#!/usr/bin/env python3
"""
Test script to verify environment variable loading
"""
import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import load_settings
from utils.logging import setup_logging

def test_env_vars():
    """Test environment variable loading"""
    print("🔧 Environment Variable Loading Test")
    print("=" * 50)
    
    # Setup logging
    setup_logging()
    
    # Load settings (this will load .env file)
    print("📁 Loading settings and .env file...")
    settings = load_settings()
    
    # Test critical environment variables
    env_vars_to_test = [
        "GEMINI_API_KEY",
        "GOOGLE_API_KEY", 
        "GROQ_API_KEY",
        "OPENROUTER_API_KEY",
        "WEATHER_API_KEY",
        "NEWS_API_KEY",
        "LIVEKIT_SERVER_URL",
        "LIVEKIT_API_KEY",
        "LIVEKIT_API_SECRET",
        "REDIS_URL",
        "DATABASE_URL",
        "ENCRYPTION_KEY",
        "SESSION_SECRET"
    ]
    
    print("\n🔍 Checking Environment Variables:")
    print("-" * 50)
    
    loaded_count = 0
    missing_count = 0
    
    for var_name in env_vars_to_test:
        value = os.getenv(var_name)
        if value:
            # Mask sensitive values for display
            if len(value) > 20:
                display_value = f"{value[:8]}...{value[-4:]}"
            else:
                display_value = f"{value[:4]}...{value[-2:]}" if len(value) > 6 else "***"
            
            print(f"✅ {var_name:<20}: {display_value}")
            loaded_count += 1
        else:
            print(f"❌ {var_name:<20}: NOT SET")
            missing_count += 1
    
    print("\n📊 Summary:")
    print("-" * 50)
    print(f"✅ Loaded: {loaded_count}")
    print(f"❌ Missing: {missing_count}")
    print(f"📈 Success Rate: {(loaded_count / len(env_vars_to_test)) * 100:.1f}%")
    
    # Test settings substitution
    print("\n⚙️ Testing Settings Substitution:")
    print("-" * 50)
    
    # Check LLM provider configurations
    llm_providers = settings.llm.get("providers", {})
    for tier, config in llm_providers.items():
        api_key = config.get("api_key", "")
        if api_key and not api_key.startswith("${"):
            print(f"✅ {tier.title()} LLM: API key substituted")
        elif api_key.startswith("${"):
            print(f"❌ {tier.title()} LLM: API key NOT substituted ({api_key})")
        else:
            print(f"⚠️  {tier.title()} LLM: No API key configured")
    
    # Check agent configurations
    agents = settings.agents
    for agent_name, agent_config in agents.items():
        if agent_config.get("enabled", False):
            api_key = agent_config.get("api_key", "")
            if api_key and not api_key.startswith("${"):
                print(f"✅ {agent_name.title()} Agent: API key substituted")
            elif api_key.startswith("${"):
                print(f"❌ {agent_name.title()} Agent: API key NOT substituted ({api_key})")
            else:
                print(f"⚠️  {agent_name.title()} Agent: No API key configured")
    
    print("\n🎯 Recommendations:")
    print("-" * 50)
    
    if missing_count == 0:
        print("🎉 All environment variables are loaded successfully!")
        print("🚀 Your Lumina AI is ready for full functionality!")
    else:
        print("📝 To resolve missing environment variables:")
        print("   1. Check that .env file exists in project root")
        print("   2. Verify .env file format (KEY=value)")
        print("   3. Ensure no spaces around = sign")
        print("   4. Check for typos in variable names")
        print("   5. Restart application after .env changes")
    
    return missing_count == 0

if __name__ == "__main__":
    success = test_env_vars()
    print(f"\n{'🎉 SUCCESS!' if success else '❌ ISSUES FOUND'}")
    sys.exit(0 if success else 1)
