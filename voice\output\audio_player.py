"""
Audio Player for Lumina AI
Handles playback of generated TTS audio through speakers
"""

import asyncio
import io
import logging
import tempfile
import threading
import time
from typing import Optional, Dict, Any
from pathlib import Path

try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False

try:
    from pydub import AudioSegment
    from pydub.playback import play
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False


class AudioPlayer:
    """Handles audio playback for TTS output"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        self.is_initialized = False
        self.is_playing = False
        self.current_playback_thread = None
        
        # Audio settings
        self.volume = self.config.get("volume", 0.7)  # 0.0 to 1.0
        self.preferred_method = self.config.get("playback_method", "auto")  # auto, pygame, pydub
        self.playback_method = None
    
    async def initialize(self):
        """Initialize audio playback system"""
        try:
            self.logger.info("Initializing audio playback system...")
            
            # Determine best available playback method
            if self.preferred_method == "pygame" and PYGAME_AVAILABLE:
                self.playback_method = "pygame"
            elif self.preferred_method == "pydub" and PYDUB_AVAILABLE:
                self.playback_method = "pydub"
            else:
                # Auto-detect best available method
                if PYGAME_AVAILABLE:
                    self.playback_method = "pygame"
                elif PYDUB_AVAILABLE:
                    self.playback_method = "pydub"
                else:
                    self.logger.error("No audio playback libraries available. Install pygame or pydub.")
                    return False
            
            # Initialize the chosen method
            if self.playback_method == "pygame":
                success = await self._initialize_pygame()
            elif self.playback_method == "pydub":
                success = await self._initialize_pydub()
            else:
                success = False
            
            if success:
                self.is_initialized = True
                self.logger.info(f"Audio playback initialized using {self.playback_method}")
            else:
                self.logger.error("Failed to initialize audio playback")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error initializing audio player: {e}")
            return False
    
    async def _initialize_pygame(self) -> bool:
        """Initialize pygame mixer for audio playback"""
        try:
            pygame.mixer.pre_init(frequency=22050, size=-16, channels=2, buffer=512)
            pygame.mixer.init()
            pygame.mixer.set_num_channels(8)  # Allow multiple simultaneous sounds
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize pygame: {e}")
            return False
    
    async def _initialize_pydub(self) -> bool:
        """Initialize pydub for audio playback"""
        try:
            # Pydub doesn't need explicit initialization
            # Just verify it's working by creating a test segment
            test_segment = AudioSegment.silent(duration=100)  # 100ms of silence
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize pydub: {e}")
            return False
    
    async def play_audio(self, audio_data: bytes, audio_format: str = "mp3") -> bool:
        """
        Play audio data through speakers
        
        Args:
            audio_data: Raw audio bytes
            audio_format: Audio format (mp3, wav, etc.)
            
        Returns:
            True if playback started successfully
        """
        try:
            if not self.is_initialized:
                self.logger.error("Audio player not initialized")
                return False
            
            if not audio_data:
                self.logger.warning("No audio data to play")
                return False
            
            self.logger.info(f"Playing audio: {len(audio_data)} bytes, format: {audio_format}")
            
            # Stop any current playback
            await self.stop_playback()
            
            # Start playback in a separate thread to avoid blocking
            if self.playback_method == "pygame":
                success = await self._play_with_pygame(audio_data, audio_format)
            elif self.playback_method == "pydub":
                success = await self._play_with_pydub(audio_data, audio_format)
            else:
                self.logger.error("No playback method available")
                return False
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error playing audio: {e}")
            return False
    
    async def _play_with_pygame(self, audio_data: bytes, audio_format: str) -> bool:
        """Play audio using pygame"""
        try:
            # Create a temporary file for pygame to load
            with tempfile.NamedTemporaryFile(suffix=f".{audio_format}", delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_file_path = temp_file.name
            
            def play_audio_thread():
                try:
                    self.is_playing = True
                    
                    # Load and play the audio
                    pygame.mixer.music.load(temp_file_path)
                    pygame.mixer.music.set_volume(self.volume)
                    pygame.mixer.music.play()
                    
                    # Wait for playback to complete
                    while pygame.mixer.music.get_busy():
                        time.sleep(0.1)
                    
                    self.is_playing = False
                    
                    # Clean up temporary file
                    try:
                        Path(temp_file_path).unlink()
                    except Exception as e:
                        self.logger.warning(f"Failed to delete temp file: {e}")
                        
                except Exception as e:
                    self.logger.error(f"Error in pygame playback thread: {e}")
                    self.is_playing = False
            
            # Start playback thread
            self.current_playback_thread = threading.Thread(target=play_audio_thread, daemon=True)
            self.current_playback_thread.start()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error in pygame playback: {e}")
            return False
    
    async def _play_with_pydub(self, audio_data: bytes, audio_format: str) -> bool:
        """Play audio using pydub"""
        try:
            def play_audio_thread():
                try:
                    self.is_playing = True
                    
                    # Load audio data into AudioSegment
                    audio_segment = AudioSegment.from_file(
                        io.BytesIO(audio_data), 
                        format=audio_format
                    )
                    
                    # Adjust volume
                    if self.volume != 1.0:
                        # Convert volume (0.0-1.0) to dB change
                        db_change = 20 * (self.volume - 1.0)  # Rough conversion
                        audio_segment = audio_segment + db_change
                    
                    # Play the audio
                    play(audio_segment)
                    
                    self.is_playing = False
                    
                except Exception as e:
                    self.logger.error(f"Error in pydub playback thread: {e}")
                    self.is_playing = False
            
            # Start playback thread
            self.current_playback_thread = threading.Thread(target=play_audio_thread, daemon=True)
            self.current_playback_thread.start()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error in pydub playback: {e}")
            return False
    
    async def stop_playback(self):
        """Stop current audio playback"""
        try:
            if self.is_playing:
                self.logger.info("Stopping audio playback...")
                
                if self.playback_method == "pygame":
                    pygame.mixer.music.stop()
                
                # Wait for playback thread to finish
                if self.current_playback_thread and self.current_playback_thread.is_alive():
                    self.current_playback_thread.join(timeout=2.0)
                
                self.is_playing = False
                
        except Exception as e:
            self.logger.error(f"Error stopping playback: {e}")
    
    async def set_volume(self, volume: float):
        """Set playback volume (0.0 to 1.0)"""
        try:
            self.volume = max(0.0, min(1.0, volume))
            
            if self.playback_method == "pygame" and self.is_playing:
                pygame.mixer.music.set_volume(self.volume)
                
        except Exception as e:
            self.logger.error(f"Error setting volume: {e}")
    
    def is_playback_active(self) -> bool:
        """Check if audio is currently playing"""
        return self.is_playing
    
    async def cleanup(self):
        """Cleanup audio player resources"""
        try:
            self.logger.info("Cleaning up audio player...")
            
            # Stop any current playback
            await self.stop_playback()
            
            # Cleanup pygame if used
            if self.playback_method == "pygame":
                pygame.mixer.quit()
            
            self.is_initialized = False
            
        except Exception as e:
            self.logger.error(f"Error during audio player cleanup: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get audio player status"""
        return {
            "is_initialized": self.is_initialized,
            "is_playing": self.is_playing,
            "playback_method": self.playback_method,
            "volume": self.volume,
            "available_methods": {
                "pygame": PYGAME_AVAILABLE,
                "pydub": PYDUB_AVAILABLE
            }
        }
