"""
Agent Registry for Lumina AI

Manages registration, discovery, and lifecycle of agents.
"""

import asyncio
from typing import Dict, List, Optional, Any, Type
from datetime import datetime

from agents.base.agent_interface import BaseAgent, AgentCapability
from config.settings import get_settings
from utils.logging import get_logger

logger = get_logger(__name__)


class AgentRegistry:
    """
    Registry for managing agents and their capabilities
    """

    def __init__(self):
        self.settings = get_settings()
        self.agents: Dict[str, BaseAgent] = {}
        self.agent_capabilities: Dict[str, List[AgentCapability]] = {}
        self.agent_metadata: Dict[str, Dict[str, Any]] = {}

    async def initialize(self):
        """Initialize the agent registry"""
        logger.info("Initializing Agent Registry")

        # Load agent configurations
        agent_configs = self.settings.agents

        # TODO: Dynamically load and register agents based on configuration
        logger.info(f"Agent registry initialized with {len(self.agents)} agents")

    def register_agent(self, agent: BaseAgent) -> bool:
        """Register an agent with the registry"""
        try:
            agent_name = agent.get_name()

            if agent_name in self.agents:
                logger.warning(f"Agent {agent_name} is already registered")
                return False

            # Register the agent
            self.agents[agent_name] = agent
            self.agent_capabilities[agent_name] = agent.get_capabilities()
            self.agent_metadata[agent_name] = {
                "registered_at": datetime.now().isoformat(),
                "version": getattr(agent, "version", "1.0.0"),
                "description": agent.get_description(),
                "status": "active",
            }

            logger.info(f"Registered agent: {agent_name}")
            return True

        except Exception as e:
            logger.error(f"Failed to register agent: {str(e)}")
            return False
