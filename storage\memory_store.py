"""
Memory storage implementation for Lumina AI
"""

import asyncio
from typing import Dict, Any, Optional, List
import logging
import json
from pathlib import Path
import time


class MemoryStore:
    """In-memory storage with optional persistence"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # In-memory storage
        self.conversations: Dict[str, List[Dict[str, Any]]] = {}
        self.user_preferences: Dict[str, Dict[str, Any]] = {}
        self.cache: Dict[str, Any] = {}
        
        # Persistence settings
        self.storage_type = config.get("type", "file")
        self.base_path = Path(config.get("file_storage", {}).get("base_path", "data"))
        self._initialized = False
    
    async def initialize(self):
        """Initialize storage"""
        try:
            self.logger.info("Initializing memory store...")
            
            # Create data directory if using file storage
            if self.storage_type == "file":
                self.base_path.mkdir(parents=True, exist_ok=True)
                await self._load_from_files()
            
            self._initialized = True
            self.logger.info("Memory store initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize memory store: {e}")
            raise
    
    async def store_conversation_turn(self, session_id: str, user_input: str, 
                                    agent_response: str, metadata: Optional[Dict[str, Any]] = None):
        """Store a conversation turn"""
        if session_id not in self.conversations:
            self.conversations[session_id] = []
        
        turn = {
            "timestamp": time.time(),
            "user_input": user_input,
            "agent_response": agent_response,
            "metadata": metadata or {}
        }
        
        self.conversations[session_id].append(turn)
        
        # Persist if configured
        if self.storage_type == "file":
            await self._save_conversations()
    
    async def get_conversation_history(self, session_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get conversation history for a session"""
        history = self.conversations.get(session_id, [])
        return history[-limit:] if limit > 0 else history
    
    async def store_user_preference(self, user_id: str, key: str, value: Any):
        """Store user preference"""
        if user_id not in self.user_preferences:
            self.user_preferences[user_id] = {}
        
        self.user_preferences[user_id][key] = value
        
        # Persist if configured
        if self.storage_type == "file":
            await self._save_user_preferences()
    
    async def get_user_preference(self, user_id: str, key: str, default: Any = None) -> Any:
        """Get user preference"""
        return self.user_preferences.get(user_id, {}).get(key, default)
    
    async def cache_set(self, key: str, value: Any, ttl: int = 3600):
        """Set cache value with TTL"""
        self.cache[key] = {
            "value": value,
            "expires_at": time.time() + ttl
        }
    
    async def cache_get(self, key: str) -> Optional[Any]:
        """Get cache value"""
        if key in self.cache:
            cache_entry = self.cache[key]
            if time.time() < cache_entry["expires_at"]:
                return cache_entry["value"]
            else:
                # Expired, remove it
                del self.cache[key]
        return None
    
    async def cache_delete(self, key: str):
        """Delete cache entry"""
        if key in self.cache:
            del self.cache[key]
    
    async def cleanup_expired_cache(self):
        """Remove expired cache entries"""
        current_time = time.time()
        expired_keys = [
            key for key, entry in self.cache.items()
            if current_time >= entry["expires_at"]
        ]
        
        for key in expired_keys:
            del self.cache[key]
        
        if expired_keys:
            self.logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")
    
    async def cleanup(self):
        """Cleanup storage resources"""
        try:
            self.logger.info("Cleaning up memory store...")
            
            # Save data if using file storage
            if self.storage_type == "file":
                await self._save_conversations()
                await self._save_user_preferences()
            
            self._initialized = False
            self.logger.info("Memory store cleanup complete")
            
        except Exception as e:
            self.logger.error(f"Error during memory store cleanup: {e}")
    
    async def _load_from_files(self):
        """Load data from files"""
        try:
            # Load conversations
            conversations_file = self.base_path / "conversations.json"
            if conversations_file.exists():
                with open(conversations_file, 'r', encoding='utf-8') as f:
                    self.conversations = json.load(f)
            
            # Load user preferences
            preferences_file = self.base_path / "user_preferences.json"
            if preferences_file.exists():
                with open(preferences_file, 'r', encoding='utf-8') as f:
                    self.user_preferences = json.load(f)
            
            self.logger.info("Data loaded from files successfully")
            
        except Exception as e:
            self.logger.warning(f"Failed to load data from files: {e}")
    
    async def _save_conversations(self):
        """Save conversations to file"""
        try:
            conversations_file = self.base_path / "conversations.json"
            with open(conversations_file, 'w', encoding='utf-8') as f:
                json.dump(self.conversations, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Failed to save conversations: {e}")
    
    async def _save_user_preferences(self):
        """Save user preferences to file"""
        try:
            preferences_file = self.base_path / "user_preferences.json"
            with open(preferences_file, 'w', encoding='utf-8') as f:
                json.dump(self.user_preferences, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Failed to save user preferences: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get storage status"""
        return {
            "initialized": self._initialized,
            "storage_type": self.storage_type,
            "conversations_count": len(self.conversations),
            "users_count": len(self.user_preferences),
            "cache_entries": len(self.cache)
        }
