"""
Voice Command Recognition System for Lumina AI
Recognizes and handles specific voice commands and intents
"""

import asyncio
import logging
import re
from typing import Dict, List, Optional, Any, Callable, Tuple
from dataclasses import dataclass
from enum import Enum
from datetime import datetime

from utils.logging import get_logger

logger = get_logger(__name__)


class CommandType(Enum):
    """Types of voice commands"""

    SYSTEM = "system"
    CONVERSATION = "conversation"
    MEDIA = "media"
    INFORMATION = "information"
    UTILITY = "utility"
    NAVIGATION = "navigation"


class CommandPriority(Enum):
    """Priority levels for commands"""

    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class VoiceCommand:
    """Represents a voice command definition"""

    command_id: str
    patterns: List[str]  # Regex patterns or keywords
    command_type: CommandType
    priority: CommandPriority
    handler: str  # Method name to call
    description: str
    languages: List[str]
    examples: List[str]
    requires_confirmation: bool = False
    metadata: Dict[str, Any] = None


@dataclass
class CommandMatch:
    """Represents a matched command"""

    command: VoiceCommand
    confidence: float
    matched_text: str
    extracted_params: Dict[str, Any]
    language: str


class VoiceCommandRecognizer:
    """Recognizes voice commands from transcribed text"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.commands: Dict[str, VoiceCommand] = {}
        self.command_handlers: Dict[str, Callable] = {}

        # Initialize built-in commands
        self._initialize_builtin_commands()

    def _initialize_builtin_commands(self):
        """Initialize built-in voice commands"""

        # System commands
        self.register_command(
            VoiceCommand(
                command_id="stop_listening",
                patterns=[
                    r"(?:stop|band|रुको|बंद)\s*(?:listening|सुनना)?",
                    r"(?:exit|निकास|बाहर)",
                    r"(?:goodbye|अलविदा|नमस्ते)",
                ],
                command_type=CommandType.SYSTEM,
                priority=CommandPriority.HIGH,
                handler="handle_stop_listening",
                description="Stop voice interaction",
                languages=["hi", "en", "mr", "gu", "ta", "te"],
                examples=[
                    "Stop listening",
                    "रुको",
                    "Goodbye",
                    "अलविदा",
                    "थांबा",
                    "બંધ કરો",
                ],
            )
        )

        self.register_command(
            VoiceCommand(
                command_id="start_listening",
                patterns=[
                    r"(?:start|शुरू)\s*(?:listening|सुनना)",
                    r"(?:wake up|जागो)",
                    r"(?:hello|हैलो|नमस्ते)\s*(?:lumina|लुमिना)?",
                ],
                command_type=CommandType.SYSTEM,
                priority=CommandPriority.HIGH,
                handler="handle_start_listening",
                description="Start voice interaction",
                languages=["hi", "en", "mr", "gu", "ta", "te"],
                examples=[
                    "Start listening",
                    "शुरू करो",
                    "Hello Lumina",
                    "नमस्ते लुमिना",
                    "सुरुवात करा",
                    "શરૂ કરો",
                ],
            )
        )

        # Information commands
        self.register_command(
            VoiceCommand(
                command_id="get_time",
                patterns=[
                    r"(?:what|क्या)\s*(?:time|समय)\s*(?:is it|है)?",
                    r"(?:current|वर्तमान)\s*(?:time|समय)",
                    r"(?:tell me|बताओ)\s*(?:the|)\s*(?:time|समय)",
                ],
                command_type=CommandType.INFORMATION,
                priority=CommandPriority.MEDIUM,
                handler="handle_get_time",
                description="Get current time",
                languages=["hi", "en", "mr", "gu", "ta", "te"],
                examples=[
                    "What time is it?",
                    "समय क्या है?",
                    "Tell me the time",
                    "समय बताओ",
                    "वेळ काय आहे?",
                    "સમય શું છે?",
                ],
            )
        )

        self.register_command(
            VoiceCommand(
                command_id="get_date",
                patterns=[
                    r"(?:what|क्या)\s*(?:date|तारीख|दिनांक)\s*(?:is it|है)?",
                    r"(?:today's|आज का)\s*(?:date|तारीख)",
                    r"(?:tell me|बताओ)\s*(?:the|)\s*(?:date|तारीख)",
                ],
                command_type=CommandType.INFORMATION,
                priority=CommandPriority.MEDIUM,
                handler="handle_get_date",
                description="Get current date",
                languages=["hi", "en"],
                examples=[
                    "What date is it?",
                    "आज की तारीख क्या है?",
                    "Tell me the date",
                    "तारीख बताओ",
                ],
            )
        )

        # Conversation commands
        self.register_command(
            VoiceCommand(
                command_id="repeat_response",
                patterns=[
                    r"(?:repeat|दोहराओ|फिर से)",
                    r"(?:say that again|फिर से कहो)",
                    r"(?:what did you say|तुमने क्या कहा)?",
                ],
                command_type=CommandType.CONVERSATION,
                priority=CommandPriority.MEDIUM,
                handler="handle_repeat_response",
                description="Repeat the last response",
                languages=["hi", "en"],
                examples=["Repeat", "दोहराओ", "Say that again", "फिर से कहो"],
            )
        )

        self.register_command(
            VoiceCommand(
                command_id="change_language",
                patterns=[
                    r"(?:change|बदलो)\s*(?:language|भाषा)\s*(?:to|में)\s*(\w+)",
                    r"(?:speak|बोलो)\s*(?:in|में)\s*(\w+)",
                    r"(\w+)\s*(?:language|भाषा)\s*(?:please|कृपया)?",
                ],
                command_type=CommandType.SYSTEM,
                priority=CommandPriority.MEDIUM,
                handler="handle_change_language",
                description="Change conversation language",
                languages=["hi", "en"],
                examples=[
                    "Change language to English",
                    "हिंदी में बोलो",
                    "English please",
                    "हिंदी कृपया",
                ],
            )
        )

        # Utility commands
        self.register_command(
            VoiceCommand(
                command_id="volume_control",
                patterns=[
                    r"(?:volume|आवाज़)\s*(up|down|increase|decrease|बढ़ाओ|कम करो)",
                    r"(?:louder|तेज़)\s*(?:please|कृपया)?",
                    r"(?:quieter|धीमे)\s*(?:please|कृपया)?",
                ],
                command_type=CommandType.UTILITY,
                priority=CommandPriority.LOW,
                handler="handle_volume_control",
                description="Control audio volume",
                languages=["hi", "en"],
                examples=["Volume up", "आवाज़ बढ़ाओ", "Louder please", "तेज़ करो"],
            )
        )

        # Help command
        self.register_command(
            VoiceCommand(
                command_id="help",
                patterns=[
                    r"(?:help|मदद|सहायता)",
                    r"(?:what can you do|तुम क्या कर सकते हो)",
                    r"(?:commands|कमांड्स|आदेश)",
                ],
                command_type=CommandType.INFORMATION,
                priority=CommandPriority.MEDIUM,
                handler="handle_help",
                description="Show available commands",
                languages=["hi", "en"],
                examples=["Help", "मदद", "What can you do?", "तुम क्या कर सकते हो?"],
            )
        )

    def register_command(self, command: VoiceCommand):
        """Register a new voice command"""
        self.commands[command.command_id] = command
        self.logger.debug(f"Registered voice command: {command.command_id}")

    def register_handler(self, handler_name: str, handler_func: Callable):
        """Register a command handler function"""
        self.command_handlers[handler_name] = handler_func
        self.logger.debug(f"Registered command handler: {handler_name}")

    async def recognize_command(
        self, text: str, language: str = "hi"
    ) -> Optional[CommandMatch]:
        """
        Recognize a voice command from text

        Args:
            text: Transcribed text to analyze
            language: Language of the text

        Returns:
            CommandMatch if a command is recognized, None otherwise
        """
        try:
            text_lower = text.lower().strip()
            best_match = None
            best_confidence = 0.0

            for command in self.commands.values():
                # Skip commands not supported in this language
                if language not in command.languages:
                    continue

                # Check each pattern
                for pattern in command.patterns:
                    match = re.search(pattern, text_lower, re.IGNORECASE)
                    if match:
                        # Calculate confidence based on match quality
                        confidence = self._calculate_confidence(
                            text_lower, pattern, match
                        )

                        if confidence > best_confidence:
                            # Extract parameters from the match
                            params = self._extract_parameters(match, command)

                            best_match = CommandMatch(
                                command=command,
                                confidence=confidence,
                                matched_text=match.group(0),
                                extracted_params=params,
                                language=language,
                            )
                            best_confidence = confidence

            if best_match and best_confidence > 0.6:  # Minimum confidence threshold
                self.logger.info(
                    f"🎯 Recognized command: {best_match.command.command_id} "
                    f"(confidence: {best_confidence:.2f})"
                )
                return best_match

            return None

        except Exception as e:
            self.logger.error(f"Error recognizing command: {e}")
            return None

    def _calculate_confidence(self, text: str, pattern: str, match: re.Match) -> float:
        """Calculate confidence score for a pattern match"""
        try:
            # Base confidence from match length vs text length
            match_length = len(match.group(0))
            text_length = len(text)
            length_ratio = match_length / text_length

            # Boost confidence for exact matches
            if match.group(0) == text:
                return 1.0

            # Boost confidence for matches at the beginning
            start_boost = 0.2 if match.start() == 0 else 0.0

            # Calculate final confidence
            confidence = min(0.9, length_ratio + start_boost + 0.3)

            return confidence

        except Exception:
            return 0.5

    def _extract_parameters(
        self, match: re.Match, command: VoiceCommand
    ) -> Dict[str, Any]:
        """Extract parameters from regex match"""
        try:
            params = {}

            # Extract captured groups
            if match.groups():
                for i, group in enumerate(match.groups()):
                    if group:
                        params[f"param_{i}"] = group.strip()

            # Command-specific parameter extraction
            if command.command_id == "change_language":
                if match.groups():
                    lang_text = match.groups()[0].lower()
                    # Map language names to codes
                    lang_mapping = {
                        "english": "en",
                        "hindi": "hi",
                        "marathi": "mr",
                        "gujarati": "gu",
                        "tamil": "ta",
                        "telugu": "te",
                        "हिंदी": "hi",
                        "अंग्रेजी": "en",
                        "मराठी": "mr",
                    }
                    params["language"] = lang_mapping.get(lang_text, lang_text)

            elif command.command_id == "volume_control":
                if match.groups():
                    action = match.groups()[0].lower()
                    if action in ["up", "increase", "बढ़ाओ", "louder", "तेज़"]:
                        params["action"] = "increase"
                    elif action in ["down", "decrease", "कम करो", "quieter", "धीमे"]:
                        params["action"] = "decrease"

            return params

        except Exception as e:
            self.logger.error(f"Error extracting parameters: {e}")
            return {}

    async def execute_command(self, command_match: CommandMatch) -> Dict[str, Any]:
        """
        Execute a recognized command

        Args:
            command_match: The matched command to execute

        Returns:
            Execution result
        """
        try:
            handler_name = command_match.command.handler

            if handler_name not in self.command_handlers:
                return {
                    "success": False,
                    "error": f"Handler not found: {handler_name}",
                    "response": "Sorry, I couldn't execute that command.",
                }

            handler = self.command_handlers[handler_name]

            # Execute the handler
            result = await handler(command_match)

            self.logger.info(f"✅ Executed command: {command_match.command.command_id}")
            return result

        except Exception as e:
            self.logger.error(f"Error executing command: {e}")
            return {
                "success": False,
                "error": str(e),
                "response": "Sorry, there was an error executing that command.",
            }

    def get_commands_by_type(self, command_type: CommandType) -> List[VoiceCommand]:
        """Get all commands of a specific type"""
        return [
            cmd for cmd in self.commands.values() if cmd.command_type == command_type
        ]

    def get_command_help(self, language: str = "hi") -> List[Dict[str, Any]]:
        """Get help information for all commands"""
        help_info = []

        for command in self.commands.values():
            if language in command.languages:
                help_info.append(
                    {
                        "command": command.command_id,
                        "description": command.description,
                        "examples": command.examples,
                        "type": command.command_type.value,
                    }
                )

        return help_info

    def get_status(self) -> Dict[str, Any]:
        """Get command recognizer status"""
        return {
            "total_commands": len(self.commands),
            "registered_handlers": len(self.command_handlers),
            "command_types": list(
                set(cmd.command_type.value for cmd in self.commands.values())
            ),
            "supported_languages": list(
                set(lang for cmd in self.commands.values() for lang in cmd.languages)
            ),
        }
