#!/usr/bin/env python3
"""
Test script to verify Lumina AI functionality
"""
import asyncio
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.app import <PERSON>minaApp
from config.settings import get_settings
from utils.logging import setup_logging
import logging


async def test_lumina_components():
    """Test Lumina AI components"""
    print("🚀 Testing Lumina AI Components...")

    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)

    try:
        # Load settings
        print("📋 Loading configuration...")
        settings = get_settings()
        print(f"✅ Configuration loaded - App: {settings.app.get('name', 'Lumina AI')}")

        # Initialize app
        print("🔧 Initializing Lumina AI...")
        app = LuminaApp(settings)
        await app.initialize()
        print("✅ Lumina AI initialized successfully!")

        # Test storage
        print("💾 Testing memory store...")
        if app.storage:
            await app.storage.cache_set("test_key", "test_value")
            value = await app.storage.cache_get("test_key")
            if value == "test_value":
                print("✅ Memory store working correctly")
            else:
                print("❌ Memory store test failed")

        # Test LLM router
        print("🤖 Testing LLM router...")
        if app.llm_router:
            print(
                f"✅ LLM Router initialized with {len(app.llm_router.providers)} providers"
            )
            for provider_name, provider in app.llm_router.providers.items():
                status = "✅ Ready" if provider.is_healthy else "⚠️ Not configured"
                print(f"   - {provider_name}: {status}")

        # Test agent router
        print("🎯 Testing agent router...")
        if app.agent_router:
            print(
                f"✅ Agent Router initialized with {len(app.agent_router.agents)} agents"
            )

        # Test voice components
        print("🎤 Testing voice components...")
        if app.voice_synthesizer:
            print("✅ Voice synthesizer initialized")
        if app.voice_handler:
            print("✅ Voice handler initialized")

        # Test cache
        print("🗄️ Testing LLM cache...")
        if hasattr(app.llm_router, "cache") and app.llm_router.cache:
            cache_stats = app.llm_router.cache.get_cache_stats()
            cache_size = cache_stats["cache_size"]
            max_size = cache_stats["max_cache_size"]
            print(f"✅ LLM Cache initialized - Size: {cache_size}/{max_size}")

        print("\n🎉 All Lumina AI components are working correctly!")
        print("\n📊 Component Summary:")
        print(f"   • App Name: {settings.app.get('name', 'Lumina AI')}")
        print(f"   • Version: {settings.app.get('version', '1.0.0')}")
        print(f"   • Default Language: {settings.app.get('default_language', 'hi')}")
        print(
            f"   • Supported Languages: {settings.app.get('supported_languages', [])}"
        )
        print(
            f"   • Voice Provider: {settings.voice.get('output', {}).get('provider', 'edge_tts')}"
        )
        print(
            f"   • LLM Providers: {len(app.llm_router.providers) if app.llm_router else 0}"
        )
        print(f"   • Agents: {len(app.agent_router.agents) if app.agent_router else 0}")

        # Cleanup
        await app.cleanup()
        print("\n✅ Cleanup completed successfully")

        return True

    except Exception as e:
        logger.error(f"Test failed: {e}")
        print(f"❌ Test failed: {e}")
        return False


async def test_edge_tts():
    """Test Edge TTS functionality"""
    print("\n🎵 Testing Edge TTS...")
    try:
        import edge_tts

        # Test list voices
        print("📋 Testing voice list...")
        voices = await edge_tts.list_voices()
        hindi_voices = [v for v in voices if v["Locale"].startswith("hi-")]
        print(f"✅ Found {len(voices)} total voices, {len(hindi_voices)} Hindi voices")

        # Test communication
        print("🗣️ Testing speech synthesis...")
        text = "नमस्ते, मैं लुमिना एआई हूं।"  # "Hello, I am Lumina AI" in Hindi
        voice = "hi-IN-SwaraNeural"

        communicate = edge_tts.Communicate(text, voice)
        audio_data = b""
        async for chunk in communicate.stream():
            if chunk["type"] == "audio":
                audio_data += chunk["data"]

        if len(audio_data) > 0:
            print(
                f"✅ Speech synthesis successful - Generated {len(audio_data)} bytes of audio"
            )
        else:
            print("❌ Speech synthesis failed - No audio data generated")

        return len(audio_data) > 0

    except ImportError:
        print("❌ Edge TTS not installed")
        return False
    except Exception as e:
        print(f"❌ Edge TTS test failed: {e}")
        return False


async def main():
    """Main test function"""
    print("=" * 60)
    print("🧪 LUMINA AI FUNCTIONALITY TEST")
    print("=" * 60)

    # Test core components
    core_test = await test_lumina_components()

    # Test Edge TTS
    tts_test = await test_edge_tts()

    # Final results
    print("\n" + "=" * 60)
    print("📋 TEST RESULTS SUMMARY")
    print("=" * 60)
    print(f"Core Components: {'✅ PASS' if core_test else '❌ FAIL'}")
    print(f"Edge TTS:        {'✅ PASS' if tts_test else '❌ FAIL'}")

    if core_test and tts_test:
        print("\n🎉 ALL TESTS PASSED! Lumina AI is ready to use!")
        print("\n📝 Next Steps:")
        print("   1. Configure API keys for LLM providers (Gemini, Groq, OpenRouter)")
        print("   2. Set up LiveKit for real-time voice communication")
        print("   3. Configure agents for specific tasks")
        print("   4. Deploy the application")
    else:
        print("\n⚠️ Some tests failed. Please check the configuration.")

    return core_test and tts_test


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
