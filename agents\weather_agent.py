"""
Weather Agent for Lumina AI

Provides weather information and forecasts.
"""

import asyncio
import json
import re
from typing import Dict, List, Optional, Any
from datetime import datetime

from agents.base.agent_interface import (
    BaseAgent,
    AgentRequest,
    AgentResponse,
    AgentCapability,
)
from config.settings import get_settings
from utils.logging import get_logger

logger = get_logger(__name__)


class WeatherAgent(BaseAgent):
    """
    Agent for handling weather-related queries
    """

    def __init__(self):
        super().__init__()
        self.settings = get_settings()
        self.name = "weather"
        self.description = "Provides weather information and forecasts"
        self.version = "1.0.0"

        # Weather API configuration
        self.api_key = self.settings.agents.get("weather", {}).get("api_key")
        self.api_url = "https://api.openweathermap.org/data/2.5"

        # Supported capabilities
        self.capabilities = [
            AgentCapability.INFORMATION_RETRIEVAL,
            AgentCapability.REAL_TIME_DATA,
        ]

        # Keywords for intent detection
        self.keywords = {
            "hi": [
                "मौसम",
                "बारिश",
                "धूप",
                "ठंड",
                "गर्मी",
                "तापमान",
                "weather",
                "rain",
                "sun",
            ],
            "en": [
                "weather",
                "temperature",
                "rain",
                "sunny",
                "cloudy",
                "forecast",
                "climate",
            ],
            "mr": ["हवामान", "पाऊस", "सूर्य", "थंडी", "उष्णता"],
            "gu": ["હવામાન", "વરસાદ", "સૂર્ય", "ઠંડક", "ગરમી"],
            "ta": ["வானிலை", "மழை", "சூரியன்", "குளிர்", "வெப்பம்"],
            "te": ["వాతావరణం", "వర్షం", "సూర్యుడు", "చల్లదనం", "వేడిమి"],
        }

    async def initialize(self) -> bool:
        """Initialize the weather agent"""
        logger.info("Initializing Weather Agent")

        if not self.api_key:
            logger.warning("Weather API key not configured")
            return False

        # Test API connectivity
        try:
            # Simple test call
            logger.info("Weather agent initialized successfully")
            return True
        except Exception as e:
            logger.error("Failed to initialize weather agent: %s", str(e))
            return False

    async def process_request(self, request: AgentRequest) -> AgentResponse:
        """Process a weather-related request"""
        try:
            user_input = request.user_input.lower()
            language = request.language

            # Extract location from user input
            location = self._extract_location(user_input, language)

            if not location:
                return AgentResponse(
                    content=self._get_location_prompt(language),
                    success=True,
                    agent_name=self.name,
                    timestamp=datetime.now(),
                    metadata={"requires_location": True},
                )

            # Get weather data
            weather_data = await self._get_weather_data(location)

            if not weather_data:
                return AgentResponse(
                    content=self._get_error_message(language),
                    success=False,
                    agent_name=self.name,
                    timestamp=datetime.now(),
                )

            # Format response
            response_text = self._format_weather_response(weather_data, language)

            return AgentResponse(
                content=response_text,
                success=True,
                agent_name=self.name,
                timestamp=datetime.now(),
                metadata={"location": location, "weather_data": weather_data},
            )

        except Exception as e:
            logger.error("Error processing weather request: %s", str(e))
            return AgentResponse(
                content=self._get_error_message(request.language),
                success=False,
                agent_name=self.name,
                timestamp=datetime.now(),
                error=str(e),
            )

    def can_handle_request(self, request: AgentRequest) -> float:
        """Check if this agent can handle the request"""
        user_input = request.user_input.lower()
        language = request.language

        # Check for weather-related keywords
        keywords = self.keywords.get(language, self.keywords["en"])

        for keyword in keywords:
            if keyword in user_input:
                return 0.8  # High confidence for weather keywords

        return 0.0

    def _extract_location(self, user_input: str, language: str) -> Optional[str]:
        """Extract location from user input"""

        # Common city names in different languages
        common_cities = {
            "hi": {
                "दिल्ली": "Delhi",
                "मुंबई": "Mumbai",
                "कोलकाता": "Kolkata",
                "चेन्नई": "Chennai",
                "बैंगलोर": "Bangalore",
                "हैदराबाद": "Hyderabad",
                "पुणे": "Pune",
                "अहमदाबाद": "Ahmedabad",
                "जयपुर": "Jaipur",
            },
            "en": {
                "delhi": "Delhi",
                "mumbai": "Mumbai",
                "kolkata": "Kolkata",
                "chennai": "Chennai",
                "bangalore": "Bangalore",
                "hyderabad": "Hyderabad",
                "pune": "Pune",
                "ahmedabad": "Ahmedabad",
                "jaipur": "Jaipur",
                "new york": "New York",
                "london": "London",
                "paris": "Paris",
                "tokyo": "Tokyo",
                "sydney": "Sydney",
            },
        }

        # Location indicators/prepositions
        location_indicators = {
            "hi": ["में", "का", "की", "के", "से"],
            "en": ["in", "at", "for", "of", "from"],
            "mr": ["मध्ये", "च्या", "ची"],
            "gu": ["માં", "ના", "ની"],
            "ta": ["இல்", "ன்", "ின்"],
            "te": ["లో", "న", "కు"],
        }

        # First, check for direct city name matches
        cities = common_cities.get(language, common_cities["en"])
        for city_key, city_name in cities.items():
            if city_key in user_input.lower():
                return city_name

        # Try to extract location using patterns
        indicators = location_indicators.get(language, location_indicators["en"])

        # Pattern: "weather in [location]" or similar
        for indicator in indicators:
            pattern = rf"{indicator}\s+([a-zA-Z\u0900-\u097F\s]+)"
            match = re.search(pattern, user_input, re.IGNORECASE)
            if match:
                location = match.group(1).strip()
                # Clean up the location (remove extra words)
                location_words = location.split()
                if location_words:
                    # Take first 1-2 words as location
                    return " ".join(location_words[:2]).title()

        # If no specific location found, return None to prompt user
        return None

    async def _get_weather_data(self, location: str) -> Optional[Dict[str, Any]]:
        """Get weather data from API"""
        if not self.api_key:
            return None

        try:
            # TODO: Implement actual API call
            # For now, return mock data
            return {
                "location": location,
                "temperature": 25,
                "description": "Clear sky",
                "humidity": 60,
                "wind_speed": 5,
            }
        except Exception as e:
            logger.error("Error fetching weather data: %s", str(e))
            return None

    def _format_weather_response(
        self, weather_data: Dict[str, Any], language: str
    ) -> str:
        """Format weather data into a response"""
        templates = {
            "hi": f"मौसम की जानकारी: तापमान {weather_data['temperature']}°C, {weather_data['description']}",
            "en": f"Weather: Temperature {weather_data['temperature']}°C, {weather_data['description']}",
            "mr": f"हवामान: तापमान {weather_data['temperature']}°C, {weather_data['description']}",
            "gu": f"હવામાન: તાપમાન {weather_data['temperature']}°C, {weather_data['description']}",
            "ta": f"வானிலை: வெப்பநிலை {weather_data['temperature']}°C, {weather_data['description']}",
            "te": f"వాతావరణం: ఉష్ణోగ్రత {weather_data['temperature']}°C, {weather_data['description']}",
        }

        return templates.get(language, templates["en"])

    def _get_location_prompt(self, language: str) -> str:
        """Get location prompt message"""
        prompts = {
            "hi": "कृपया बताएं कि आप किस शहर का मौसम जानना चाहते हैं?",
            "en": "Please tell me which city's weather you'd like to know?",
            "mr": "कृपया सांगा की तुम्हाला कोणत्या शहराचे हवामान जाणून घ्यायचे आहे?",
            "gu": "કૃપા કરીને કહો કે તમે કયા શહેરનું હવામાન જાણવા માંગો છો?",
            "ta": "எந்த நகரத்தின் வானிலையை அறிய விரும்புகிறீர்கள் என்று சொல்லுங்கள்?",
            "te": "దయచేసి మీరు ఏ నగరం యొక్క వాతావరణాన్ని తెలుసుకోవాలనుకుంటున్నారో చెప్పండి?",
        }

        return prompts.get(language, prompts["en"])

    def _get_error_message(self, language: str) -> str:
        """Get error message"""
        messages = {
            "hi": "मुझे मौसम की जानकारी प्राप्त करने में समस्या हो रही है। कृपया बाद में कोशिश करें।",
            "en": "I'm having trouble getting weather information. Please try again later.",
            "mr": "मला हवामानाची माहिती मिळवण्यात अडचण येत आहे. कृपया नंतर प्रयत्न करा.",
            "gu": "મને હવામાનની માહિતી મેળવવામાં મુશ્કેલી આવી રહી છે. કૃપા કરીને પછીથી પ્રયાસ કરો.",
            "ta": "வானிலை தகவல்களைப் பெறுவதில் சிக்கல் உள்ளது. பின்னர் முயற்சிக்கவும்.",
            "te": "వాతావరణ సమాచారాన్ని పొందడంలో ఇబ్బంది ఉంది. దయచేసి తర్వాత ప్రయత్నించండి.",
        }

        return messages.get(language, messages["en"])
