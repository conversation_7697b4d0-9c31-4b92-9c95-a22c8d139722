"""
Context Manager for Lumina AI

Handles conversation context, session management, and context retention
across multi-turn interactions.
"""

import asyncio
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field

from config.settings import get_settings
from storage.memory_store import MemoryStore
from utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class ConversationTurn:
    """Represents a single turn in a conversation"""

    turn_id: str
    user_input: str
    agent_response: str
    timestamp: datetime
    agent_type: str
    confidence: float
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SessionContext:
    """Represents a user session context"""

    session_id: str
    user_id: Optional[str]
    language: str
    created_at: datetime
    last_activity: datetime
    conversation_history: List[ConversationTurn] = field(default_factory=list)
    user_preferences: Dict[str, Any] = field(default_factory=dict)
    active_agents: List[str] = field(default_factory=list)
    context_variables: Dict[str, Any] = field(default_factory=dict)


class ContextManager:
    """
    Manages conversation context and session state for Lumina AI
    """

    def __init__(self, storage: MemoryStore):
        self.storage = storage
        self.settings = get_settings()
        self.active_sessions: Dict[str, SessionContext] = {}
        self.context_window_size = self.settings.performance.get(
            "context_window_size", 4000
        )
        self.session_timeout = timedelta(
            hours=self.settings.performance.get("session_timeout_hours", 24)
        )

    async def create_session(
        self, user_id: Optional[str] = None, language: str = "hi"
    ) -> str:
        """Create a new conversation session"""
        session_id = str(uuid.uuid4())
        now = datetime.now()

        session = SessionContext(
            session_id=session_id,
            user_id=user_id,
            language=language,
            created_at=now,
            last_activity=now,
        )

        self.active_sessions[session_id] = session
        await self.storage.store_session(session_id, session.__dict__)

        logger.info(f"Created new session: {session_id} for user: {user_id}")
        return session_id

    async def get_session(self, session_id: str) -> Optional[SessionContext]:
        """Retrieve session context"""
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            if self._is_session_expired(session):
                await self.cleanup_session(session_id)
                return None
            return session

        # Try to load from storage
        session_data = await self.storage.get_session(session_id)
        if session_data:
            session = SessionContext(**session_data)
            if not self._is_session_expired(session):
                self.active_sessions[session_id] = session
                return session
            else:
                await self.cleanup_session(session_id)

        return None

    async def add_conversation_turn(
        self,
        session_id: str,
        user_input: str,
        agent_response: str,
        agent_type: str,
        confidence: float,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """Add a conversation turn to the session"""
        session = await self.get_session(session_id)
        if not session:
            logger.warning(f"Session not found: {session_id}")
            return False

        turn = ConversationTurn(
            turn_id=str(uuid.uuid4()),
            user_input=user_input,
            agent_response=agent_response,
            timestamp=datetime.now(),
            agent_type=agent_type,
            confidence=confidence,
            metadata=metadata or {},
        )

        session.conversation_history.append(turn)
        session.last_activity = datetime.now()

        # Maintain context window size
        if len(session.conversation_history) > self.context_window_size:
            session.conversation_history = session.conversation_history[
                -self.context_window_size :
            ]

        # Update storage
        await self.storage.store_session(session_id, session.__dict__)

        logger.debug(f"Added conversation turn to session: {session_id}")
        return True

    async def get_conversation_context(
        self, session_id: str, max_turns: Optional[int] = None
    ) -> List[ConversationTurn]:
        """Get conversation history for context"""
        session = await self.get_session(session_id)
        if not session:
            return []

        history = session.conversation_history
        if max_turns:
            history = history[-max_turns:]

        return history

    async def update_user_preferences(
        self, session_id: str, preferences: Dict[str, Any]
    ) -> bool:
        """Update user preferences for the session"""
        session = await self.get_session(session_id)
        if not session:
            return False

        session.user_preferences.update(preferences)
        session.last_activity = datetime.now()

        await self.storage.store_session(session_id, session.__dict__)
        logger.debug(f"Updated preferences for session: {session_id}")
        return True

    async def set_context_variable(self, session_id: str, key: str, value: Any) -> bool:
        """Set a context variable for the session"""
        session = await self.get_session(session_id)
        if not session:
            return False

        session.context_variables[key] = value
        session.last_activity = datetime.now()

        await self.storage.store_session(session_id, session.__dict__)
        return True

    async def get_context_variable(
        self, session_id: str, key: str, default: Any = None
    ) -> Any:
        """Get a context variable from the session"""
        session = await self.get_session(session_id)
        if not session:
            return default

        return session.context_variables.get(key, default)

    async def cleanup_session(self, session_id: str) -> bool:
        """Clean up an expired or ended session"""
        if session_id in self.active_sessions:
            del self.active_sessions[session_id]

        await self.storage.delete_session(session_id)
        logger.info(f"Cleaned up session: {session_id}")
        return True

    async def cleanup_expired_sessions(self) -> int:
        """Clean up all expired sessions"""
        expired_count = 0
        expired_sessions = []

        for session_id, session in self.active_sessions.items():
            if self._is_session_expired(session):
                expired_sessions.append(session_id)

        for session_id in expired_sessions:
            await self.cleanup_session(session_id)
            expired_count += 1

        logger.info(f"Cleaned up {expired_count} expired sessions")
        return expired_count

    def _is_session_expired(self, session: SessionContext) -> bool:
        """Check if a session has expired"""
        return datetime.now() - session.last_activity > self.session_timeout

    async def get_session_stats(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get statistics for a session"""
        session = await self.get_session(session_id)
        if not session:
            return None

        return {
            "session_id": session.session_id,
            "user_id": session.user_id,
            "language": session.language,
            "created_at": session.created_at.isoformat(),
            "last_activity": session.last_activity.isoformat(),
            "conversation_turns": len(session.conversation_history),
            "active_agents": session.active_agents,
            "context_variables_count": len(session.context_variables),
        }
