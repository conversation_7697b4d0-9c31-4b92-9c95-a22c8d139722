"""
Voice Coordinator for Lumina AI

Manages speech-to-text and text-to-speech operations.
"""

import asyncio
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime

from voice.base.stt_interface import STTInterface, STTResult
from voice.base.tts_interface import TTSInterface, TTSResult, Voice
from voice.stt.whisper_stt import WhisperST<PERSON>
from voice.tts.edge_tts import EdgeTTS
from voice.output.audio_player import AudioPlayer
from config.settings import get_settings
from utils.logging import get_logger

logger = get_logger(__name__)


class VoiceCoordinator:
    """
    Coordinates speech-to-text and text-to-speech operations
    """

    def __init__(self):
        self.settings = get_settings()
        self.stt_engine: Optional[STTInterface] = None
        self.tts_engine: Optional[TTSInterface] = None
        self.audio_player: Optional[AudioPlayer] = None
        self.is_initialized = False

        # Voice processing configuration
        self.default_language = self.settings.app.get("default_language", "hi")
        self.voice_settings = self.settings.voice

        # Performance metrics
        self.metrics = {
            "stt_requests": 0,
            "tts_requests": 0,
            "total_processing_time": 0.0,
            "errors": 0,
        }

    async def initialize(self) -> bool:
        """Initialize voice coordinator and engines"""
        logger.info("Initializing Voice Coordinator")

        try:
            # Initialize STT engine
            stt_success = await self._initialize_stt()

            # Initialize TTS engine
            tts_success = await self._initialize_tts()

            # Initialize Audio Player
            audio_success = await self._initialize_audio_player()

            if stt_success and tts_success and audio_success:
                self.is_initialized = True
                logger.info("Voice Coordinator initialized successfully")
                return True
            else:
                logger.warning(
                    f"Voice Coordinator partially initialized (STT: {stt_success}, TTS: {tts_success}, Audio: {audio_success})"
                )
                return stt_success and tts_success  # Audio player is optional

        except Exception as e:
            logger.error(f"Failed to initialize Voice Coordinator: {str(e)}")
            return False

    async def _initialize_stt(self) -> bool:
        """Initialize Speech-to-Text engine"""
        try:
            stt_engine = self.voice_settings.get("stt_engine", "whisper")

            if stt_engine == "whisper":
                self.stt_engine = WhisperSTT()
            else:
                logger.warning(f"Unknown STT engine: {stt_engine}, using Whisper")
                self.stt_engine = WhisperSTT()

            success = await self.stt_engine.initialize()
            if success:
                logger.info(f"STT engine ({self.stt_engine.get_name()}) initialized")
            else:
                logger.error("Failed to initialize STT engine")

            return success

        except Exception as e:
            logger.error(f"Error initializing STT: {str(e)}")
            return False

    async def _initialize_tts(self) -> bool:
        """Initialize Text-to-Speech engine"""
        try:
            tts_engine = self.voice_settings.get("tts_engine", "edge")

            if tts_engine == "edge":
                self.tts_engine = EdgeTTS()
            else:
                logger.warning(f"Unknown TTS engine: {tts_engine}, using Edge TTS")
                self.tts_engine = EdgeTTS()

            success = await self.tts_engine.initialize()
            if success:
                logger.info(f"TTS engine ({self.tts_engine.get_name()}) initialized")
            else:
                logger.error("Failed to initialize TTS engine")

            return success

        except Exception as e:
            logger.error(f"Error initializing TTS: {str(e)}")
            return False

    async def _initialize_audio_player(self) -> bool:
        """Initialize Audio Player"""
        try:
            self.audio_player = AudioPlayer()
            success = await self.audio_player.initialize()
            if success:
                logger.info("Audio player initialized")
            else:
                logger.error("Failed to initialize audio player")
            return success
        except Exception as e:
            logger.error(f"Error initializing audio player: {str(e)}")
            return False

    async def speech_to_text(
        self, audio_data: bytes, language: Optional[str] = None
    ) -> STTResult:
        """
        Convert speech to text

        Args:
            audio_data: Raw audio bytes
            language: Target language code (optional)

        Returns:
            STTResult with transcription
        """
        start_time = datetime.now()

        try:
            if not self.stt_engine or not self.stt_engine.is_available():
                return STTResult(
                    text="",
                    confidence=0.0,
                    language="",
                    processing_time=0.0,
                    success=False,
                    error="STT engine not available",
                )

            # Use default language if not specified
            target_language = language or self.default_language

            # Perform transcription
            result = await self.stt_engine.transcribe_audio(audio_data, target_language)

            # Update metrics
            self.metrics["stt_requests"] += 1
            self.metrics["total_processing_time"] += result.processing_time

            if not result.success:
                self.metrics["errors"] += 1

            logger.info(
                f"STT completed: '{result.text[:50]}...' (conf: {result.confidence:.2f})"
            )

            return result

        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            self.metrics["errors"] += 1
            logger.error(f"Error in speech-to-text: {str(e)}")

            return STTResult(
                text="",
                confidence=0.0,
                language="",
                processing_time=processing_time,
                success=False,
                error=str(e),
            )

    async def text_to_speech(
        self,
        text: str,
        language: Optional[str] = None,
        voice_name: Optional[str] = None,
        speech_rate: str = "medium",
        speech_volume: str = "medium",
    ) -> TTSResult:
        """
        Convert text to speech

        Args:
            text: Text to synthesize
            language: Language code (optional)
            voice_name: Specific voice name (optional)
            speech_rate: Speech rate (x-slow, slow, medium, fast, x-fast)
            speech_volume: Speech volume (x-soft, soft, medium, loud, x-loud)

        Returns:
            TTSResult with audio data
        """
        start_time = datetime.now()

        try:
            if not self.tts_engine or not self.tts_engine.is_available():
                return TTSResult(
                    audio_data=b"",
                    format="wav",
                    sample_rate=24000,
                    duration=0.0,
                    processing_time=0.0,
                    success=False,
                    error="TTS engine not available",
                )

            # Use default language if not specified
            target_language = language or self.default_language

            # Create voice configuration if voice_name is specified
            voice = None
            if voice_name:
                voice = Voice(
                    name=voice_name,
                    language=target_language,
                    gender="female",  # Default
                    style=None,
                )

            # Perform synthesis
            result = await self.tts_engine.synthesize_speech(
                text, voice, target_language, speech_rate, speech_volume
            )

            # Update metrics
            self.metrics["tts_requests"] += 1
            self.metrics["total_processing_time"] += result.processing_time

            if not result.success:
                self.metrics["errors"] += 1

            logger.info(
                f"TTS completed: {len(result.audio_data)} bytes, {result.duration:.1f}s"
            )

            return result

        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            self.metrics["errors"] += 1
            logger.error(f"Error in text-to-speech: {str(e)}")

            return TTSResult(
                audio_data=b"",
                format="wav",
                sample_rate=24000,
                duration=0.0,
                processing_time=processing_time,
                success=False,
                error=str(e),
            )

    async def process_voice_request(
        self, audio_data: bytes, language: Optional[str] = None
    ) -> Tuple[STTResult, Optional[str]]:
        """
        Process voice input and return transcription with detected intent

        Args:
            audio_data: Raw audio bytes
            language: Target language code (optional)

        Returns:
            Tuple of (STTResult, detected_intent)
        """
        try:
            # Convert speech to text
            stt_result = await self.speech_to_text(audio_data, language)

            if not stt_result.success:
                return stt_result, None

            # Simple intent detection (can be enhanced)
            detected_intent = self._detect_intent(stt_result.text)

            return stt_result, detected_intent

        except Exception as e:
            logger.error(f"Error processing voice request: {str(e)}")
            return (
                STTResult(
                    text="",
                    confidence=0.0,
                    language="",
                    processing_time=0.0,
                    success=False,
                    error=str(e),
                ),
                None,
            )

    def _detect_intent(self, text: str) -> Optional[str]:
        """Simple intent detection from text"""
        text_lower = text.lower()

        # Basic intent patterns
        if any(word in text_lower for word in ["weather", "मौसम", "हवा"]):
            return "weather"
        elif any(word in text_lower for word in ["remind", "याद", "अलार्म"]):
            return "reminder"
        elif any(word in text_lower for word in ["news", "समाचार", "खबर"]):
            return "news"
        elif any(word in text_lower for word in ["translate", "अनुवाद"]):
            return "translation"
        elif any(word in text_lower for word in ["summary", "सारांश"]):
            return "summarization"
        else:
            return "general"

    def get_available_voices(self, language: Optional[str] = None) -> List[Voice]:
        """Get available TTS voices"""
        if self.tts_engine:
            return self.tts_engine.get_available_voices(language)
        return []

    def get_supported_languages(self) -> Dict[str, List[str]]:
        """Get supported languages for both STT and TTS"""
        result = {"stt": [], "tts": []}

        if self.stt_engine:
            result["stt"] = self.stt_engine.get_supported_languages()

        if self.tts_engine:
            result["tts"] = self.tts_engine.get_supported_languages()

        return result

    def get_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        return self.metrics.copy()

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on voice components"""
        health_data = {
            "voice_coordinator": {
                "status": "healthy" if self.is_initialized else "unhealthy",
                "initialized": self.is_initialized,
                "metrics": self.metrics,
            }
        }

        if self.stt_engine:
            health_data["stt"] = await self.stt_engine.health_check()

        if self.tts_engine:
            health_data["tts"] = await self.tts_engine.health_check()

        return health_data

    async def transcribe_audio(
        self, audio_data: bytes, language: str = "hi"
    ) -> Dict[str, Any]:
        """
        Transcribe audio data to text

        Args:
            audio_data: Raw audio bytes
            language: Expected language

        Returns:
            Dictionary with transcription result
        """
        try:
            result = await self.speech_to_text(audio_data, language)

            return {
                "success": result.success,
                "text": result.text,
                "confidence": result.confidence,
                "language": result.language,
                "processing_time": result.processing_time,
                "error": result.error if not result.success else None,
            }

        except Exception as e:
            logger.error(f"Error transcribing audio: {e}")
            return {
                "success": False,
                "text": "",
                "confidence": 0.0,
                "language": language,
                "processing_time": 0.0,
                "error": str(e),
            }

    async def synthesize_speech(
        self, text: str, language: str = "hi"
    ) -> Dict[str, Any]:
        """
        Synthesize speech from text

        Args:
            text: Text to synthesize
            language: Target language

        Returns:
            Dictionary with synthesis result
        """
        try:
            result = await self.text_to_speech(text, language)

            return {
                "success": result.success,
                "audio_data": result.audio_data,
                "format": result.format,
                "duration": result.duration,
                "voice": result.voice,
                "error": result.error if not result.success else None,
            }

        except Exception as e:
            logger.error(f"Error synthesizing speech: {e}")
            return {
                "success": False,
                "audio_data": None,
                "format": "",
                "duration": 0.0,
                "voice": "",
                "error": str(e),
            }

    async def play_audio(self, audio_data: bytes, format: str = "mp3") -> bool:
        """
        Play audio data

        Args:
            audio_data: Audio data to play
            format: Audio format

        Returns:
            True if successful, False otherwise
        """
        try:
            if not self.audio_player:
                logger.error("Audio player not initialized")
                return False

            success = await self.audio_player.play_audio(audio_data, format)
            return success

        except Exception as e:
            logger.error(f"Error playing audio: {e}")
            return False

    async def cleanup(self) -> None:
        """Cleanup voice coordinator and engines"""
        try:
            if self.stt_engine:
                await self.stt_engine.cleanup()

            if self.tts_engine:
                await self.tts_engine.cleanup()

            if self.audio_player:
                await self.audio_player.cleanup()

            self.is_initialized = False
            logger.info("Voice Coordinator cleanup completed")

        except Exception as e:
            logger.error(f"Error during Voice Coordinator cleanup: {str(e)}")
