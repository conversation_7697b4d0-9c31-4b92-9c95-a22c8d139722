"""
Text-to-Speech interface for Lumina AI
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List, Union
from dataclasses import dataclass
from datetime import datetime
import io


@dataclass
class TTSResult:
    """Result from text-to-speech synthesis"""
    audio_data: bytes
    format: str  # e.g., "wav", "mp3", "ogg"
    sample_rate: int
    duration: float  # in seconds
    processing_time: float
    success: bool
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    timestamp: Optional[datetime] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class Voice:
    """Voice configuration for TTS"""
    name: str
    language: str
    gender: str  # "male", "female", "neutral"
    style: Optional[str] = None  # e.g., "cheerful", "sad", "excited"
    speed: float = 1.0  # Speech rate multiplier
    pitch: float = 1.0  # Pitch multiplier
    volume: float = 1.0  # Volume multiplier


class TTSInterface(ABC):
    """Abstract interface for Text-to-Speech implementations"""
    
    def __init__(self):
        self.is_initialized = False
        self.name = ""
        self.version = "1.0.0"
        self.default_voice: Optional[Voice] = None
    
    @abstractmethod
    async def initialize(self) -> bool:
        """
        Initialize the TTS service
        
        Returns:
            True if initialization successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def synthesize_speech(
        self, 
        text: str, 
        voice: Optional[Voice] = None,
        language: Optional[str] = None
    ) -> TTSResult:
        """
        Synthesize speech from text
        
        Args:
            text: Text to synthesize
            voice: Voice configuration (optional)
            language: Language code (optional)
            
        Returns:
            TTSResult with audio data
        """
        pass
    
    @abstractmethod
    async def synthesize_ssml(
        self, 
        ssml: str, 
        voice: Optional[Voice] = None
    ) -> TTSResult:
        """
        Synthesize speech from SSML
        
        Args:
            ssml: SSML markup
            voice: Voice configuration (optional)
            
        Returns:
            TTSResult with audio data
        """
        pass
    
    @abstractmethod
    def get_available_voices(self, language: Optional[str] = None) -> List[Voice]:
        """
        Get list of available voices
        
        Args:
            language: Filter by language (optional)
            
        Returns:
            List of available voices
        """
        pass
    
    @abstractmethod
    def get_supported_languages(self) -> List[str]:
        """
        Get list of supported language codes
        
        Returns:
            List of language codes
        """
        pass
    
    @abstractmethod
    def get_supported_formats(self) -> List[str]:
        """
        Get list of supported audio formats
        
        Returns:
            List of format names
        """
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup resources"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if TTS service is available"""
        pass
    
    @abstractmethod
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check"""
        pass
    
    def get_name(self) -> str:
        """Get TTS service name"""
        return self.name
    
    def get_version(self) -> str:
        """Get TTS service version"""
        return self.version
    
    def set_default_voice(self, voice: Voice) -> None:
        """Set default voice"""
        self.default_voice = voice
    
    def get_default_voice(self) -> Optional[Voice]:
        """Get default voice"""
        return self.default_voice
    
    def create_voice(
        self,
        name: str,
        language: str,
        gender: str,
        style: Optional[str] = None,
        speed: float = 1.0,
        pitch: float = 1.0,
        volume: float = 1.0
    ) -> Voice:
        """
        Create a voice configuration
        
        Args:
            name: Voice name
            language: Language code
            gender: Voice gender
            style: Voice style (optional)
            speed: Speech rate multiplier
            pitch: Pitch multiplier
            volume: Volume multiplier
            
        Returns:
            Voice configuration
        """
        return Voice(
            name=name,
            language=language,
            gender=gender,
            style=style,
            speed=speed,
            pitch=pitch,
            volume=volume
        )
