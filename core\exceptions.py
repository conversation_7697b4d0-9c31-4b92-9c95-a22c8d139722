"""
Custom exceptions for Lumina AI
"""


class LuminaException(Exception):
    """Base exception for Lumina AI"""
    pass


class VoiceProcessingException(LuminaException):
    """Exception raised during voice processing"""
    pass


class LLMProviderException(LuminaException):
    """Exception raised by LLM providers"""
    pass


class AgentException(LuminaException):
    """Exception raised by agents"""
    pass


class ConfigurationException(LuminaException):
    """Exception raised for configuration errors"""
    pass


class AuthenticationException(LuminaException):
    """Exception raised for authentication errors"""
    pass


class RateLimitException(LuminaException):
    """Exception raised when rate limits are exceeded"""
    pass


class TimeoutException(LuminaException):
    """Exception raised when operations timeout"""
    pass
