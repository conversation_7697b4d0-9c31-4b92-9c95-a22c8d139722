"""
Summarization Agent for Lumina AI

Provides text summarization capabilities.
"""

import asyncio
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
import re

from agents.base.agent_interface import BaseAgent, AgentRequest, AgentResponse, AgentCapability
from config.settings import get_settings
from utils.logging import get_logger

logger = get_logger(__name__)


class SummarizationAgent(BaseAgent):
    """
    Agent for handling text summarization requests
    """
    
    def __init__(self):
        super().__init__()
        self.settings = get_settings()
        self.name = "summarization"
        self.description = "Provides text summarization capabilities"
        self.version = "1.0.0"
        
        # Supported capabilities
        self.capabilities = [
            AgentCapability.SUMMARIZATION,
            AgentCapability.INFORMATION_RETRIEVAL
        ]
        
        # Keywords for intent detection
        self.keywords = {
            "hi": ["सारांश", "संक्षेप", "छोटा करो", "समरी", "मुख्य बातें", "संक्षिप्त"],
            "en": ["summary", "summarize", "brief", "shorten", "condense", "key points", "main points"],
            "mr": ["सारांश", "संक्षेप", "छोटे करा", "मुख्य मुद्दे"],
            "gu": ["સારાંશ", "સંક્ષેપ", "મુખ્ય મુદ્દાઓ", "ટૂંકું કરો"],
            "ta": ["சுருக்கம்", "முக்கிய புள்ளிகள்", "சுருக்கமாக"],
            "te": ["సారాంశం", "సంక్షిప్తం", "ముఖ్య అంశాలు"]
        }
        
        # Summarization types
        self.summary_types = {
            "hi": {
                "छोटा": "brief",
                "विस्तृत": "detailed",
                "मुख्य बातें": "bullet_points"
            },
            "en": {
                "brief": "brief",
                "detailed": "detailed",
                "bullet points": "bullet_points",
                "key points": "bullet_points"
            }
        }
    
    async def initialize(self) -> bool:
        """Initialize the summarization agent"""
        logger.info("Initializing Summarization Agent")
        
        try:
            # Initialize any required resources
            logger.info("Summarization agent initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize summarization agent: {str(e)}")
            return False
    
    async def process_request(self, request: AgentRequest) -> AgentResponse:
        """Process a summarization request"""
        try:
            user_input = request.user_input
            language = request.language
            
            # Extract text to summarize
            text_to_summarize = self._extract_text_to_summarize(user_input, language)
            
            if not text_to_summarize:
                return AgentResponse(
                    content=self._get_text_prompt(language),
                    success=True,
                    agent_name=self.name,
                    timestamp=datetime.now(),
                    metadata={"requires_text": True}
                )
            
            # Determine summary type
            summary_type = self._extract_summary_type(user_input, language)
            
            # Generate summary
            summary = await self._generate_summary(text_to_summarize, summary_type, language)
            
            if not summary:
                return AgentResponse(
                    content=self._get_error_message(language),
                    success=False,
                    agent_name=self.name,
                    timestamp=datetime.now()
                )
            
            # Format response
            response_text = self._format_summary_response(summary, summary_type, language)
            
            return AgentResponse(
                content=response_text,
                success=True,
                agent_name=self.name,
                timestamp=datetime.now(),
                metadata={
                    "summary_type": summary_type,
                    "original_length": len(text_to_summarize),
                    "summary_length": len(summary)
                }
            )
            
        except Exception as e:
            logger.error(f"Error processing summarization request: {str(e)}")
            return AgentResponse(
                content=self._get_error_message(request.language),
                success=False,
                agent_name=self.name,
                timestamp=datetime.now(),
                error=str(e)
            )
    
    def can_handle_request(self, request: AgentRequest) -> float:
        """Check if this agent can handle the request"""
        user_input = request.user_input.lower()
        language = request.language
        
        # Check for summarization-related keywords
        keywords = self.keywords.get(language, self.keywords["en"])
        
        for keyword in keywords:
            if keyword in user_input:
                return 0.8  # High confidence for summarization keywords
        
        # Check for long text that might need summarization
        if len(user_input.split()) > 50:  # More than 50 words
            return 0.3  # Medium confidence for long text
        
        return 0.0
    
    def _extract_text_to_summarize(self, user_input: str, language: str) -> str:
        """Extract text to summarize from user input"""
        # Remove summarization command words
        text = user_input
        
        # Remove common summarization phrases
        remove_phrases = {
            "hi": ["सारांश बनाओ", "संक्षेप करो", "छोटा करो", "समरी दो"],
            "en": ["summarize", "give summary", "make brief", "condense"]
        }
        
        phrases = remove_phrases.get(language, remove_phrases["en"])
        for phrase in phrases:
            text = text.replace(phrase, "").strip()
        
        # If text is still very short, it might be a request without content
        if len(text.split()) < 10:
            return ""
        
        return text
    
    def _extract_summary_type(self, user_input: str, language: str) -> str:
        """Extract summary type from user input"""
        user_input = user_input.lower()
        summary_types = self.summary_types.get(language, self.summary_types["en"])
        
        for type_name, type_code in summary_types.items():
            if type_name in user_input:
                return type_code
        
        return "brief"  # Default type
    
    async def _generate_summary(self, text: str, summary_type: str, language: str) -> Optional[str]:
        """Generate summary of the text"""
        try:
            # For now, implement a simple extractive summarization
            # In production, this would use an LLM or advanced summarization model
            
            sentences = self._split_into_sentences(text)
            
            if summary_type == "brief":
                # Return first 2 sentences or 20% of sentences, whichever is smaller
                num_sentences = min(2, max(1, len(sentences) // 5))
            elif summary_type == "detailed":
                # Return 50% of sentences
                num_sentences = max(1, len(sentences) // 2)
            elif summary_type == "bullet_points":
                # Extract key points as bullet points
                return self._extract_bullet_points(sentences, language)
            else:
                num_sentences = min(3, len(sentences))
            
            # Select most important sentences (for now, just take first N)
            # In production, use sentence scoring algorithms
            summary_sentences = sentences[:num_sentences]
            
            return " ".join(summary_sentences)
            
        except Exception as e:
            logger.error(f"Error generating summary: {str(e)}")
            return None
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """Split text into sentences"""
        # Simple sentence splitting - in production, use proper NLP
        sentences = re.split(r'[.!?]+', text)
        sentences = [s.strip() for s in sentences if s.strip()]
        return sentences
    
    def _extract_bullet_points(self, sentences: List[str], language: str) -> str:
        """Extract key points as bullet points"""
        # Simple implementation - take every other sentence as key points
        key_sentences = sentences[::2]  # Take every 2nd sentence
        
        bullet_symbol = "•"
        bullet_points = [f"{bullet_symbol} {sentence}" for sentence in key_sentences[:5]]  # Max 5 points
        
        headers = {
            "hi": "मुख्य बातें:",
            "en": "Key Points:",
            "mr": "मुख्य मुद्दे:",
            "gu": "મુખ્ય મુદ્દાઓ:",
            "ta": "முக்கிய புள்ளிகள்:",
            "te": "ముఖ్య అంశాలు:"
        }
        
        header = headers.get(language, headers["en"])
        return f"{header}\n\n" + "\n".join(bullet_points)
    
    def _format_summary_response(self, summary: str, summary_type: str, language: str) -> str:
        """Format the summary response"""
        headers = {
            "hi": {
                "brief": "📝 संक्षिप्त सारांश:",
                "detailed": "📄 विस्तृत सारांश:",
                "bullet_points": "📋 मुख्य बातें:"
            },
            "en": {
                "brief": "📝 Brief Summary:",
                "detailed": "📄 Detailed Summary:",
                "bullet_points": "📋 Key Points:"
            }
        }
        
        lang_headers = headers.get(language, headers["en"])
        header = lang_headers.get(summary_type, lang_headers["brief"])
        
        return f"{header}\n\n{summary}"
    
    def _get_text_prompt(self, language: str) -> str:
        """Get text prompt message"""
        prompts = {
            "hi": "कृपया वह टेक्स्ट दें जिसका सारांश बनाना है।",
            "en": "Please provide the text you want me to summarize.",
            "mr": "कृपया तो मजकूर द्या ज्याचा सारांश हवा आहे.",
            "gu": "કૃપા કરીને તે ટેક્સ્ટ આપો જેનો સારાંશ જોઈએ છે.",
            "ta": "தயவுசெய்து சுருக்க வேண்டிய உரையை வழங்கவும்.",
            "te": "దయచేసి సారాంశం చేయాల్సిన టెక్స్ట్ ఇవ్వండి."
        }
        
        return prompts.get(language, prompts["en"])
    
    def _get_error_message(self, language: str) -> str:
        """Get error message"""
        messages = {
            "hi": "सारांश बनाने में समस्या हो रही है। कृपया बाद में कोशिश करें।",
            "en": "I'm having trouble creating the summary. Please try again later.",
            "mr": "सारांश तयार करण्यात अडचण येत आहे. कृपया नंतर प्रयत्न करा.",
            "gu": "સારાંશ બનાવવામાં મુશ્કેલી આવી રહી છે. કૃપા કરીને પછીથી પ્રયાસ કરો.",
            "ta": "சுருக்கம் உருவாக்குவதில் சிக்கல் உள்ளது. பின்னர் முயற்சிக்கவும்.",
            "te": "సారాంశం రూపొందించడంలో ఇబ్బంది ఉంది. దయచేసి తర్వాత ప్రయత్నించండి."
        }
        
        return messages.get(language, messages["en"])
