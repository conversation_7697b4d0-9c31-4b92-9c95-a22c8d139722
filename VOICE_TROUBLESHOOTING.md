# 🎤 Lumina AI Voice Troubleshooting Guide

## 🚨 **Common Error: "The system cannot find the file specified"**

This error typically occurs during voice transcription and can have several causes:

### **Root Causes:**

1. **Missing PyAudio** (Most Common)
2. **Whisper Dependencies Not Installed**
3. **Temporary File Access Issues**
4. **Windows File Path Problems**

---

## 🔧 **Quick Fix Solutions**

### **Solution 1: Install Voice Dependencies (Recommended)**

```bash
# Run the automated installer
python install_voice_dependencies.py
```

This script will:
- ✅ Detect your operating system
- ✅ Install PyAudio with proper Windows support
- ✅ Install Whisper STT dependencies
- ✅ Install Edge TTS
- ✅ Provide OS-specific installation guidance

### **Solution 2: Manual PyAudio Installation**

#### **Windows:**
```bash
# Option 1: Standard pip install
pip install pyaudio

# Option 2: If above fails, use pre-compiled wheel
pip install pipwin
pipwin install pyaudio

# Option 3: Conda (if using Anaconda)
conda install pyaudio
```

#### **Linux (Ubuntu/Debian):**
```bash
sudo apt-get update
sudo apt-get install portaudio19-dev python3-pyaudio
pip install pyaudio
```

#### **macOS:**
```bash
brew install portaudio
pip install pyaudio
```

### **Solution 3: Install Whisper Dependencies**

```bash
pip install openai-whisper torch torchvision torchaudio
```

---

## ⚙️ **Configuration Fixes**

### **Temporary Fix: Disable Voice Input**

If you want to run Lumina AI without voice input temporarily:

1. **Edit `config/settings.yaml`:**
```yaml
voice:
  input:
    provider: "livekit"  # This disables real microphone input
```

2. **Or use environment variable:**
```bash
export VOICE_INPUT_DISABLED=true
python main.py
```

### **Enable Voice Input After Installation**

After installing dependencies, update `config/settings.yaml`:
```yaml
voice:
  input:
    provider: "microphone"  # Enable real microphone input
```

---

## 🔍 **Diagnostic Commands**

### **Check Current Status:**
```bash
# Test all components
python test_lumina.py

# Check environment variables
python test_env_vars.py

# Run system demo
python demo_lumina.py
```

### **Check Dependencies:**
```python
# Run this in Python to check what's installed
import sys

try:
    import pyaudio
    print("✅ PyAudio is installed")
except ImportError:
    print("❌ PyAudio is NOT installed")

try:
    import whisper
    print("✅ Whisper is installed")
except ImportError:
    print("❌ Whisper is NOT installed")

try:
    import torch
    print("✅ PyTorch is installed")
except ImportError:
    print("❌ PyTorch is NOT installed")
```

---

## 🐛 **Advanced Troubleshooting**

### **Windows-Specific Issues:**

1. **Visual Studio Build Tools Missing:**
   - Download and install Microsoft C++ Build Tools
   - Or install Visual Studio Community with C++ workload

2. **Permission Issues:**
   - Run command prompt as Administrator
   - Check Windows Defender/Antivirus settings

3. **Path Length Issues:**
   - Enable long path support in Windows
   - Use shorter directory names

### **Temporary Directory Issues:**

If you see "Cannot write to temporary directory":

```python
import tempfile
import os

temp_dir = tempfile.gettempdir()
print(f"Temp directory: {temp_dir}")
print(f"Writable: {os.access(temp_dir, os.W_OK)}")
```

**Fix:** Set custom temp directory:
```bash
set TEMP=C:\temp
set TMP=C:\temp
```

---

## 📊 **Expected Log Messages**

### **Successful Voice Setup:**
```
✅ Whisper STT initialized successfully on cpu
📊 Model: base (74MB)
🎤 Microphone voice input started
```

### **Error Indicators:**
```
❌ PyAudio not available. Install with: pip install pyaudio
❌ Whisper dependencies not available
❌ Error during transcription: [WinError 2]
```

---

## 🎯 **Testing Voice Functionality**

### **Step-by-Step Test:**

1. **Install dependencies:**
   ```bash
   python install_voice_dependencies.py
   ```

2. **Update configuration:**
   ```yaml
   # config/settings.yaml
   voice:
     input:
       provider: "microphone"
   ```

3. **Test the system:**
   ```bash
   python test_lumina.py
   ```

4. **Run in production:**
   ```bash
   python main.py
   ```

---

## 🆘 **Still Having Issues?**

### **Collect Debug Information:**

1. **Enable debug logging:**
   ```yaml
   # config/settings.yaml
   app:
     debug: true
   monitoring:
     logging:
       level: "DEBUG"
   ```

2. **Check logs:**
   ```bash
   tail -f logs/lumina.log
   ```

3. **System information:**
   ```bash
   python -c "import platform; print(platform.platform())"
   python -c "import sys; print(sys.version)"
   pip list | grep -E "(pyaudio|whisper|torch)"
   ```

### **Common Solutions Summary:**

| Error | Solution |
|-------|----------|
| `[WinError 2] The system cannot find the file specified` | Install PyAudio + Whisper |
| `PyAudio not available` | Run `python install_voice_dependencies.py` |
| `Whisper dependencies not available` | `pip install openai-whisper torch` |
| `Cannot write to temporary directory` | Check permissions, set custom TEMP |
| `Microsoft Visual C++ 14.0 is required` | Install Visual Studio Build Tools |

---

## ✅ **Success Checklist**

- [ ] PyAudio installed and working
- [ ] Whisper dependencies installed
- [ ] Voice input provider set to "microphone"
- [ ] No errors in `python test_lumina.py`
- [ ] Microphone permissions granted
- [ ] Temporary directory accessible

**Once all items are checked, your voice functionality should work perfectly!** 🎉
