"""
Microsoft Edge Text-to-Speech implementation for Lumina AI
"""

import asyncio
import io
import tempfile
import os
from typing import Optional, Dict, Any, List
from datetime import datetime

try:
    import edge_tts
    import aiofiles

    EDGE_TTS_AVAILABLE = True
except ImportError:
    EDGE_TTS_AVAILABLE = False

from voice.base.tts_interface import TTSInterface, TTSResult, Voice
from config.settings import get_settings
from utils.logging import get_logger

logger = get_logger(__name__)


class EdgeTTS(TTSInterface):
    """
    Microsoft Edge Text-to-Speech implementation
    """

    def __init__(self):
        super().__init__()
        self.settings = get_settings()
        self.name = "Microsoft Edge TTS"
        self.version = "1.0.0"
        self.available_voices: List[Voice] = []

        # Default voices for different languages
        self.default_voices = {
            "hi": "hi-IN-SwaraNeural",
            "en": "en-US-AriaNeural",
            "mr": "mr-IN-<PERSON>arohiNeural",
            "gu": "gu-IN-DhwaniNeural",
            "ta": "ta-IN-PallaviNeural",
            "te": "te-IN-ShrutiNeural",
            "bn": "bn-IN-<PERSON><PERSON><PERSON>Neural",
            "kn": "kn-IN-SapnaNeural",
            "ml": "ml-IN-SobhanaNeural",
            "pa": "pa-IN-GaganNeural",
            "or": "or-IN-SubhasiniNeural",
        }

        # Voice configurations
        self.voice_configs = {
            "hi-IN-SwaraNeural": {"gender": "female", "style": "cheerful"},
            "hi-IN-MadhurNeural": {"gender": "male", "style": "calm"},
            "en-US-AriaNeural": {"gender": "female", "style": "cheerful"},
            "en-US-GuyNeural": {"gender": "male", "style": "newscast"},
            "mr-IN-AarohiNeural": {"gender": "female", "style": "cheerful"},
            "mr-IN-ManoharNeural": {"gender": "male", "style": "calm"},
        }

    async def initialize(self) -> bool:
        """Initialize Edge TTS"""
        logger.info("Initializing Microsoft Edge TTS")

        if not EDGE_TTS_AVAILABLE:
            logger.error("Edge TTS not available. Install with: pip install edge-tts")
            return False

        try:
            # Load available voices
            await self._load_available_voices()

            # Set default voice
            default_voice_name = self.default_voices.get("hi", "hi-IN-SwaraNeural")
            self.default_voice = self._create_voice_from_name(default_voice_name)

            logger.info(
                f"Edge TTS initialized successfully with {len(self.available_voices)} voices"
            )
            self.is_initialized = True
            return True

        except Exception as e:
            logger.error(f"Failed to initialize Edge TTS: {str(e)}")
            return False

    def _convert_rate_to_edge_format(self, speech_rate: str) -> str:
        """
        Convert our speech rate format to Edge TTS rate format

        Args:
            speech_rate: Our rate format (x-slow, slow, medium, fast, x-fast)

        Returns:
            Edge TTS compatible rate string
        """
        rate_mapping = {
            "x-slow": "-50%",
            "slow": "-25%",
            "medium": "+0%",
            "fast": "+25%",
            "x-fast": "+50%",
        }
        return rate_mapping.get(speech_rate, "+0%")

    def _convert_volume_to_edge_format(self, speech_volume: str) -> str:
        """
        Convert our speech volume format to Edge TTS volume format

        Args:
            speech_volume: Our volume format (x-soft, soft, medium, loud, x-loud)

        Returns:
            Edge TTS compatible volume string
        """
        volume_mapping = {
            "x-soft": "-50%",
            "soft": "-25%",
            "medium": "+0%",
            "loud": "+25%",
            "x-loud": "+50%",
        }
        return volume_mapping.get(speech_volume, "+0%")

    async def synthesize_speech(
        self,
        text: str,
        voice: Optional[Voice] = None,
        language: Optional[str] = None,
        speech_rate: str = "medium",
        speech_volume: str = "medium",
    ) -> TTSResult:
        """
        Synthesize speech from text using Edge TTS

        Args:
            text: Text to synthesize
            voice: Voice configuration (optional)
            language: Language code (optional)
            speech_rate: Speech rate (x-slow, slow, medium, fast, x-fast)
            speech_volume: Speech volume (x-soft, soft, medium, loud, x-loud)

        Returns:
            TTSResult with audio data
        """
        start_time = datetime.now()

        try:
            if not self.is_initialized:
                return TTSResult(
                    audio_data=b"",
                    format="wav",
                    sample_rate=24000,
                    duration=0.0,
                    processing_time=0.0,
                    success=False,
                    error="Edge TTS not initialized",
                )

            # Determine voice to use
            if voice:
                voice_name = voice.name
            elif language and language in self.default_voices:
                voice_name = self.default_voices[language]
            else:
                voice_name = (
                    self.default_voice.name
                    if self.default_voice
                    else "hi-IN-SwaraNeural"
                )

            # Create TTS communicator with rate and volume control
            communicate = edge_tts.Communicate(
                text,
                voice_name,
                rate=self._convert_rate_to_edge_format(speech_rate),
                volume=self._convert_volume_to_edge_format(speech_volume),
            )

            # Generate audio
            audio_data = b""
            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    audio_data += chunk["data"]

            processing_time = (datetime.now() - start_time).total_seconds()

            # Estimate duration (rough calculation)
            # Assuming average speaking rate of 150 words per minute
            word_count = len(text.split())
            estimated_duration = (word_count / 150) * 60

            logger.info(
                f"Speech synthesis completed: {len(audio_data)} bytes, {estimated_duration:.1f}s"
            )

            return TTSResult(
                audio_data=audio_data,
                format="mp3",  # Edge TTS returns MP3 by default
                sample_rate=24000,
                duration=estimated_duration,
                processing_time=processing_time,
                success=True,
                metadata={
                    "voice": voice_name,
                    "text_length": len(text),
                    "word_count": word_count,
                    "service": "Microsoft Edge TTS",
                },
            )

        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"Error during speech synthesis: {str(e)}")

            return TTSResult(
                audio_data=b"",
                format="wav",
                sample_rate=24000,
                duration=0.0,
                processing_time=processing_time,
                success=False,
                error=str(e),
            )

    async def synthesize_ssml(
        self, ssml: str, voice: Optional[Voice] = None
    ) -> TTSResult:
        """
        Synthesize speech from SSML

        Args:
            ssml: SSML markup
            voice: Voice configuration (optional)

        Returns:
            TTSResult with audio data
        """
        # For Edge TTS, SSML is handled the same way as regular text
        # The voice parameter in Communicate() handles SSML automatically
        return await self.synthesize_speech(ssml, voice)

    def get_available_voices(self, language: Optional[str] = None) -> List[Voice]:
        """Get list of available voices"""
        if language:
            return [v for v in self.available_voices if v.language.startswith(language)]
        return self.available_voices.copy()

    def get_supported_languages(self) -> List[str]:
        """Get list of supported language codes"""
        return list(self.default_voices.keys())

    def get_supported_formats(self) -> List[str]:
        """Get list of supported audio formats"""
        return ["mp3", "wav"]

    async def _load_available_voices(self) -> None:
        """Load available voices from Edge TTS"""
        try:
            voices_data = await edge_tts.list_voices()

            for voice_data in voices_data:
                # Extract language code (e.g., "hi-IN" -> "hi")
                locale = voice_data.get("Locale", "")
                language = locale.split("-")[0] if locale else "unknown"

                # Create Voice object
                voice = Voice(
                    name=voice_data.get("ShortName", ""),
                    language=language,
                    gender=voice_data.get("Gender", "unknown").lower(),
                    style=(
                        voice_data.get("StyleList", [None])[0]
                        if voice_data.get("StyleList")
                        else None
                    ),
                )

                self.available_voices.append(voice)

            logger.info(f"Loaded {len(self.available_voices)} voices from Edge TTS")

        except Exception as e:
            logger.warning(f"Could not load voices from Edge TTS: {str(e)}")
            # Create fallback voices
            self._create_fallback_voices()

    def _create_fallback_voices(self) -> None:
        """Create fallback voices if loading fails"""
        for lang, voice_name in self.default_voices.items():
            config = self.voice_configs.get(
                voice_name, {"gender": "female", "style": None}
            )
            voice = Voice(
                name=voice_name,
                language=lang,
                gender=config["gender"],
                style=config.get("style"),
            )
            self.available_voices.append(voice)

    def _create_voice_from_name(self, voice_name: str) -> Voice:
        """Create Voice object from voice name"""
        # Extract language from voice name (e.g., "hi-IN-SwaraNeural" -> "hi")
        parts = voice_name.split("-")
        language = parts[0] if parts else "unknown"

        config = self.voice_configs.get(voice_name, {"gender": "female", "style": None})

        return Voice(
            name=voice_name,
            language=language,
            gender=config["gender"],
            style=config.get("style"),
        )

    async def cleanup(self) -> None:
        """Cleanup resources"""
        try:
            self.available_voices.clear()
            self.default_voice = None
            self.is_initialized = False
            logger.info("Edge TTS cleanup completed")

        except Exception as e:
            logger.error(f"Error during Edge TTS cleanup: {str(e)}")

    def is_available(self) -> bool:
        """Check if Edge TTS is available"""
        return EDGE_TTS_AVAILABLE and self.is_initialized

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check"""
        return {
            "service": "Microsoft Edge TTS",
            "status": "healthy" if self.is_available() else "unhealthy",
            "dependencies_available": EDGE_TTS_AVAILABLE,
            "initialized": self.is_initialized,
            "available_voices": len(self.available_voices),
            "supported_languages": len(self.get_supported_languages()),
            "default_voice": self.default_voice.name if self.default_voice else None,
        }
