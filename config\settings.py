"""
Configuration management for Lumina AI
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass
import logging

try:
    from dotenv import load_dotenv

    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False

from config.models import LuminaSettings


def load_settings(config_path: Optional[str] = None) -> LuminaSettings:
    """Load application settings from YAML files and environment variables"""

    # Load environment variables from .env file
    if DOTENV_AVAILABLE:
        # Look for .env file in project root (parent of config directory)
        project_root = Path(__file__).parent.parent
        env_file = project_root / ".env"

        if env_file.exists():
            load_dotenv(env_file)
            logging.info("Loaded environment variables from %s", env_file)
        else:
            # Try loading from current directory
            load_dotenv()
            logging.info("Attempted to load .env from current directory")
    else:
        logging.warning(
            "python-dotenv not available, environment variables must be set manually"
        )

    if config_path is None:
        config_path = Path(__file__).parent / "settings.yaml"

    try:
        # Load main settings
        with open(config_path, "r", encoding="utf-8") as f:
            config_data = yaml.safe_load(f)

        # Load language settings
        languages_path = Path(__file__).parent / "languages.yaml"
        with open(languages_path, "r", encoding="utf-8") as f:
            languages_data = yaml.safe_load(f)

        # Load agent settings
        agents_path = Path(__file__).parent / "agents.yaml"
        with open(agents_path, "r", encoding="utf-8") as f:
            agents_data = yaml.safe_load(f)

        # Merge configurations
        config_data["languages"] = languages_data
        config_data["agents"].update(agents_data.get("agents", {}))
        config_data["agent_routing"] = agents_data.get("routing", {})
        config_data["agent_communication"] = agents_data.get("communication", {})

        # Substitute environment variables
        config_data = _substitute_env_vars(config_data)

        # Create settings object
        settings = LuminaSettings(
            app=config_data.get("app", {}),
            voice=config_data.get("voice", {}),
            llm=config_data.get("llm", {}),
            agents=config_data.get("agents", {}),
            performance=config_data.get("performance", {}),
            monitoring=config_data.get("monitoring", {}),
            security=config_data.get("security", {}),
            storage=config_data.get("storage", {}),
        )

        # Add additional computed settings
        settings.languages = config_data.get("languages", {})
        settings.agent_routing = config_data.get("agent_routing", {})
        settings.agent_communication = config_data.get("agent_communication", {})
        settings.livekit = config_data.get("livekit", {})

        return settings

    except Exception as e:
        logging.error(f"Failed to load settings: {e}")
        raise


def _substitute_env_vars(data: Any) -> Any:
    """Recursively substitute environment variables in configuration"""
    if isinstance(data, dict):
        return {key: _substitute_env_vars(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [_substitute_env_vars(item) for item in data]
    elif isinstance(data, str) and data.startswith("${") and data.endswith("}"):
        # Extract environment variable name
        env_var = data[2:-1]
        default_value = None

        # Check for default value syntax: ${VAR_NAME:default_value}
        if ":" in env_var:
            env_var, default_value = env_var.split(":", 1)

        # Get environment variable value
        value = os.getenv(env_var, default_value)

        if value is None:
            logging.warning(
                f"Environment variable {env_var} not found and no default provided"
            )
            return data  # Return original string if env var not found

        return value
    else:
        return data


def get_env_var(
    name: str, default: Optional[str] = None, required: bool = False
) -> Optional[str]:
    """Get environment variable with optional default and required validation"""
    value = os.getenv(name, default)

    if required and value is None:
        raise ValueError(f"Required environment variable {name} is not set")

    return value


def validate_settings(settings: LuminaSettings) -> bool:
    """Validate settings configuration"""
    try:
        # Validate required API keys
        required_keys = []

        # Check LLM provider keys
        for provider_config in settings.llm.get("providers", {}).values():
            if provider_config.get("api_key") and provider_config["api_key"].startswith(
                "${"
            ):
                # Extract env var name
                env_var = provider_config["api_key"][2:-1].split(":")[0]
                if not os.getenv(env_var):
                    required_keys.append(env_var)

        # Check agent API keys
        for agent_name, agent_config in settings.agents.items():
            if agent_config.get("enabled", False) and agent_config.get("api_key"):
                if agent_config["api_key"].startswith("${"):
                    env_var = agent_config["api_key"][2:-1].split(":")[0]
                    if not os.getenv(env_var):
                        required_keys.append(env_var)

        if required_keys:
            logging.warning(f"Missing required environment variables: {required_keys}")
            return False

        # Validate voice configuration
        if not settings.voice.get("input", {}).get("provider"):
            logging.error("Voice input provider not configured")
            return False

        if not settings.voice.get("output", {}).get("provider"):
            logging.error("Voice output provider not configured")
            return False

        # Validate supported languages
        supported_languages = settings.app.get("supported_languages", [])
        if not supported_languages:
            logging.error("No supported languages configured")
            return False

        default_language = settings.app.get("default_language")
        if default_language not in supported_languages:
            logging.error(
                f"Default language {default_language} not in supported languages"
            )
            return False

        logging.info("Settings validation passed")
        return True

    except Exception as e:
        logging.error(f"Settings validation failed: {e}")
        return False


def create_data_directories(settings: LuminaSettings):
    """Create necessary data directories"""
    try:
        # Create logs directory
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)

        # Create data directory for file storage
        if settings.storage.get("type") == "file":
            data_dir = Path(
                settings.storage.get("file_storage", {}).get("base_path", "data")
            )
            data_dir.mkdir(exist_ok=True)

        # Create cache directory
        cache_dir = Path("cache")
        cache_dir.mkdir(exist_ok=True)

        logging.info("Data directories created successfully")

    except Exception as e:
        logging.error(f"Failed to create data directories: {e}")
        raise


# Global settings instance
_settings_instance = None


def get_settings() -> LuminaSettings:
    """Get the global settings instance"""
    global _settings_instance
    if _settings_instance is None:
        _settings_instance = load_settings()
    return _settings_instance


def set_settings(settings: LuminaSettings):
    """Set the global settings instance"""
    global _settings_instance
    _settings_instance = settings
