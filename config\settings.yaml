app:
  name: "Lumina AI"
  version: "1.0.0"
  debug: false
  default_language: "hi"
  supported_languages: ["hi", "en", "mr", "gu", "ta", "te"]
  max_session_duration: 86400 # 24 hours in seconds

voice:
  input:
    provider: "microphone" # livekit, microphone, whisper, azure
    sample_rate: 16000
    channels: 1
    chunk_size: 1024
    silence_threshold: 0.01
    silence_duration: 2.0 # seconds
    # PyAudio installed successfully - microphone input enabled
  output:
    provider: "edge_tts" # edge_tts, azure, google
    voice_mapping:
      hi: "hi-IN-SwaraNeural"
      en: "en-US-AriaNeural"
      mr: "mr-IN-AarohiNeural"
      gu: "gu-IN-DhwaniNeural"
      ta: "ta-IN-PallaviNeural"
      te: "te-IN-ShrutiNeural"
    speech_rate: "+0%"
    speech_pitch: "+0Hz"

llm:
  providers:
    primary:
      name: "gemini"
      api_key: "${GEMINI_API_KEY}"
      model: "gemini-pro"
      max_tokens: 150
      temperature: 0.7
      timeout: 10
    secondary:
      name: "groq"
      api_key: "${GROQ_API_KEY}"
      model: "mixtral-8x7b-32768"
      max_tokens: 150
      temperature: 0.7
      timeout: 8
    fallback:
      name: "openrouter"
      api_key: "${OPENROUTER_API_KEY}"
      model: "meta-llama/llama-2-70b-chat"
      max_tokens: 150
      temperature: 0.7
      timeout: 12
  circuit_breaker:
    failure_threshold: 3
    recovery_timeout: 300 # 5 minutes
    half_open_max_calls: 5

agents:
  weather:
    enabled: true
    api_key: "${WEATHER_API_KEY}"
    api_url: "https://api.openweathermap.org/data/2.5"
    default_location: "Mumbai"
    cache_duration: 1800 # 30 minutes
    units: "metric"
  reminder:
    enabled: true
    storage_type: "file" # file, redis, postgres
    storage_path: "data/reminders.json"
    max_reminders_per_user: 100
    default_timezone: "Asia/Kolkata"
  news:
    enabled: true
    api_key: "${NEWS_API_KEY}"
    sources: ["times-of-india", "ndtv", "hindustan-times"]
    language_preference: "hi"
    cache_duration: 3600 # 1 hour
    max_articles: 5
  summarization:
    enabled: true
    max_input_length: 5000
    summary_ratio: 0.3
    language_support: ["hi", "en"]
  translation:
    enabled: true
    provider: "google" # google, azure, libre
    api_key: "${TRANSLATE_API_KEY}"
    supported_pairs:
      - ["hi", "en"]
      - ["en", "hi"]
      - ["mr", "hi"]
      - ["gu", "hi"]

performance:
  cache:
    llm_responses: true
    ttl: 3600 # 1 hour
    max_size: 1000
    redis_url: "${REDIS_URL}"
  timeouts:
    llm_request: 10
    voice_processing: 5
    agent_processing: 15
    total_request: 30
  concurrency:
    max_concurrent_requests: 10
    max_agents_per_request: 3
    worker_threads: 4
  rate_limiting:
    requests_per_minute: 60
    requests_per_hour: 1000

security:
  encryption_key: "${ENCRYPTION_KEY}"
  session_secret: "${SESSION_SECRET}"
  cors_origins: ["http://localhost:3000", "http://localhost:8080"]
  max_input_length: 1000
  sanitize_input: true

monitoring:
  metrics:
    enabled: true
    port: 8080
    path: "/metrics"
  logging:
    level: "INFO"
    format: "user_friendly"
    file: "logs/lumina.log"
    max_file_size: "10MB"
    backup_count: 5
  health_check:
    enabled: true
    path: "/health"
    interval: 30

storage:
  type: "file" # file, redis, postgres
  file_storage:
    base_path: "data"
    conversations_file: "conversations.json"
    user_preferences_file: "user_preferences.json"
  redis:
    url: "${REDIS_URL}"
    db: 0
    max_connections: 10
  postgres:
    url: "${DATABASE_URL}"
    pool_size: 5
    max_overflow: 10

livekit:
  server_url: "${LIVEKIT_SERVER_URL}"
  api_key: "${LIVEKIT_API_KEY}"
  api_secret: "${LIVEKIT_API_SECRET}"
  room_name: "lumina-voice-room"
  participant_name: "lumina-ai"
