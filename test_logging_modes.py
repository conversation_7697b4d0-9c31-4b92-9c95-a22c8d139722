#!/usr/bin/env python3
"""
Test script to demonstrate the new logging modes in Lumina AI
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logging import setup_logging
from config.settings import load_settings


async def test_production_mode():
    """Test user-friendly logging mode (production)"""
    print("\n" + "="*60)
    print("🎨 PRODUCTION MODE - User-Friendly Logging")
    print("="*60)
    
    # Setup user-friendly logging
    setup_logging(
        level="INFO",
        log_format="user_friendly",
        debug_mode=False
    )
    
    # Get loggers for different components
    main_logger = logging.getLogger("main")
    voice_logger = logging.getLogger("voice.input.handler")
    llm_logger = logging.getLogger("llm.router")
    agent_logger = logging.getLogger("agents.weather")
    storage_logger = logging.getLogger("storage.memory_store")
    app_logger = logging.getLogger("core.app")
    
    # Simulate application startup sequence
    main_logger.info("Starting Lumina AI...")
    
    await asyncio.sleep(0.5)
    voice_logger.info("Initializing voice processing...")
    
    await asyncio.sleep(0.3)
    llm_logger.info("Initializing LLM providers...")
    
    await asyncio.sleep(0.4)
    agent_logger.info("Initializing AI agents...")
    
    await asyncio.sleep(0.2)
    storage_logger.info("Initializing memory store...")
    
    await asyncio.sleep(0.3)
    voice_logger.info("Voice processing initialized successfully")
    llm_logger.info("LLM providers loaded successfully")
    agent_logger.info("AI agents initialized successfully")
    storage_logger.info("Memory store loaded successfully")
    
    await asyncio.sleep(0.5)
    app_logger.info("Starting main application loop...")
    
    await asyncio.sleep(0.3)
    voice_logger.info("Voice input activated")
    
    # Simulate some warnings and errors
    await asyncio.sleep(0.5)
    main_logger.warning("API rate limit approaching")
    
    await asyncio.sleep(0.3)
    llm_logger.error("Failed to connect to backup LLM provider")
    
    await asyncio.sleep(0.5)
    main_logger.info("Application running normally")
    
    print("\n✅ Production mode demonstration complete!")


async def test_debug_mode():
    """Test JSON logging mode (debug/development)"""
    print("\n" + "="*60)
    print("🔧 DEBUG MODE - Technical JSON Logging")
    print("="*60)
    
    # Setup JSON logging
    setup_logging(
        level="DEBUG",
        log_format="json",
        debug_mode=True
    )
    
    # Get loggers for different components
    main_logger = logging.getLogger("main")
    voice_logger = logging.getLogger("voice.input.handler")
    llm_logger = logging.getLogger("llm.router")
    
    # Simulate some technical logging
    main_logger.debug("Debug information: Configuration loaded")
    voice_logger.info("Voice handler initialized with sample_rate=16000")
    llm_logger.warning("LLM provider response time: 2.3s (threshold: 2.0s)")
    main_logger.error("Database connection failed", extra={"retry_count": 3, "error_code": "CONN_TIMEOUT"})
    
    print("\n✅ Debug mode demonstration complete!")


async def test_settings_based_logging():
    """Test logging configuration from settings.yaml"""
    print("\n" + "="*60)
    print("⚙️ SETTINGS-BASED LOGGING")
    print("="*60)
    
    # Load settings
    settings = load_settings()
    
    # Get current debug mode
    debug_mode = settings.app.get('debug', False)
    logging_config = settings.monitoring.get('logging', {})
    
    print(f"Current debug mode: {debug_mode}")
    print(f"Logging configuration: {logging_config}")
    
    # Setup logging based on settings
    setup_logging(
        level=logging_config.get('level', 'INFO'),
        log_format=logging_config.get('format', 'json'),
        debug_mode=debug_mode
    )
    
    logger = logging.getLogger("settings_test")
    
    if debug_mode:
        logger.info("Running in debug mode - JSON logging enabled")
    else:
        logger.info("Running in production mode - user-friendly logging enabled")
    
    logger.info("Settings-based logging test completed")
    
    print("\n✅ Settings-based logging demonstration complete!")


async def main():
    """Main test function"""
    print("🧪 Lumina AI Logging Modes Test")
    print("This script demonstrates the dual-mode logging system")
    
    try:
        # Test production mode (user-friendly)
        await test_production_mode()
        
        # Wait a bit between tests
        await asyncio.sleep(1)
        
        # Test debug mode (JSON)
        await test_debug_mode()
        
        # Wait a bit between tests
        await asyncio.sleep(1)
        
        # Test settings-based logging
        await test_settings_based_logging()
        
        print("\n" + "="*60)
        print("🎉 All logging mode tests completed successfully!")
        print("="*60)
        print("\nTo change logging modes:")
        print("1. Edit config/settings.yaml - set app.debug to true/false")
        print("2. Or modify monitoring.logging.format to 'json' or 'user_friendly'")
        print("\nProduction mode (debug: false) = User-friendly with colors and emojis")
        print("Debug mode (debug: true) = Technical JSON logging")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
