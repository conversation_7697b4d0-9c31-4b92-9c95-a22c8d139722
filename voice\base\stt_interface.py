"""
Speech-to-Text interface for Lumina AI
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List, AsyncIterator
from dataclasses import dataclass
from datetime import datetime


@dataclass
class STTResult:
    """Result from speech-to-text transcription"""
    text: str
    confidence: float
    language: str
    processing_time: float
    success: bool
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    timestamp: Optional[datetime] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class STTInterface(ABC):
    """Abstract interface for Speech-to-Text implementations"""
    
    def __init__(self):
        self.is_initialized = False
        self.name = ""
        self.version = "1.0.0"
    
    @abstractmethod
    async def initialize(self) -> bool:
        """
        Initialize the STT service
        
        Returns:
            True if initialization successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def transcribe_audio(self, audio_data: bytes, language: Optional[str] = None) -> STTResult:
        """
        Transcribe audio data to text
        
        Args:
            audio_data: Raw audio bytes
            language: Target language code (optional)
            
        Returns:
            STTResult with transcription
        """
        pass
    
    @abstractmethod
    async def transcribe_stream(self, audio_stream) -> AsyncIterator[STTResult]:
        """
        Transcribe streaming audio data
        
        Args:
            audio_stream: Audio stream
            
        Yields:
            STTResult for each transcription chunk
        """
        pass
    
    @abstractmethod
    def get_supported_languages(self) -> List[str]:
        """
        Get list of supported language codes
        
        Returns:
            List of language codes
        """
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup resources"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if STT service is available"""
        pass
    
    @abstractmethod
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check"""
        pass
    
    def get_name(self) -> str:
        """Get STT service name"""
        return self.name
    
    def get_version(self) -> str:
        """Get STT service version"""
        return self.version
