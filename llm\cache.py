"""
LLM Response Cache for Lumina AI

Provides caching functionality for LLM responses to improve performance
and reduce API costs.
"""

import hashlib
import json
from datetime import datetime, timedelta
from typing import List, Optional, Any

from llm.providers.base_provider import LLMRequest, LLMResponse
from storage.memory_store import MemoryStore
from config.settings import get_settings
from utils.logging import get_logger

logger = get_logger(__name__)


class LLMCache:
    """
    Cache for LLM responses with TTL and size management
    """

    def __init__(self, storage: MemoryStore):
        self.storage = storage
        self.settings = get_settings()

        # Cache configuration
        self.cache_ttl = timedelta(hours=self.settings.llm.cache.ttl_hours)
        self.max_cache_size = self.settings.llm.cache.max_size
        self.cache_enabled = self.settings.llm.cache.enabled

        # Cache statistics
        self.cache_hits = 0
        self.cache_misses = 0
        self.cache_size = 0

        # Cache key prefix
        self.cache_prefix = "llm_cache:"

    async def get_cached_response(self, request: LLMRequest) -> Optional[LLMResponse]:
        """Get cached response for a request"""
        if not self.cache_enabled:
            return None

        try:
            cache_key = self._generate_cache_key(request)
            cached_data = await self.storage.get(cache_key)

            if cached_data:
                # Check if cache entry is still valid
                cache_entry = json.loads(cached_data)
                cached_time = datetime.fromisoformat(cache_entry["timestamp"])

                if datetime.now() - cached_time < self.cache_ttl:
                    # Cache hit
                    self.cache_hits += 1
                    logger.debug("Cache hit for key: %s", cache_key)

                    # Deserialize response
                    response_data = cache_entry["response"]
                    response = LLMResponse(
                        content=response_data["content"],
                        success=response_data["success"],
                        provider=response_data["provider"],
                        model=response_data["model"],
                        timestamp=datetime.fromisoformat(response_data["timestamp"]),
                        metadata=response_data.get("metadata", {}),
                    )

                    return response

                # Cache expired, remove entry
                await self.storage.delete(cache_key)
                logger.debug("Cache expired for key: %s", cache_key)

            # Cache miss
            self.cache_misses += 1
            return None

        except (json.JSONDecodeError, KeyError, ValueError) as e:
            logger.error("Error retrieving from cache: %s", str(e))
            return None

    async def cache_response(self, request: LLMRequest, response: LLMResponse) -> bool:
        """Cache a response"""
        if not self.cache_enabled or not response.success:
            return False

        try:
            cache_key = self._generate_cache_key(request)

            # Prepare cache entry
            cache_entry = {
                "timestamp": datetime.now().isoformat(),
                "request": {
                    "user_input": request.user_input,
                    "language": request.language,
                    "system_prompt": request.system_prompt,
                    "context_hash": self._hash_context(request.conversation_history),
                },
                "response": {
                    "content": response.content,
                    "success": response.success,
                    "error": response.error,
                    "provider": response.provider,
                    "model": response.model,
                    "timestamp": response.timestamp.isoformat(),
                    "metadata": response.metadata,
                },
            }

            # Check cache size and cleanup if needed
            await self._cleanup_cache_if_needed()

            # Store in cache
            await self.storage.set(
                cache_key,
                json.dumps(cache_entry),
                ttl=int(self.cache_ttl.total_seconds()),
            )

            self.cache_size += 1
            logger.debug("Cached response for key: %s", cache_key)
            return True

        except (TypeError, ValueError) as e:
            logger.error("Error caching response: %s", str(e))
            return False

    def _generate_cache_key(self, request: LLMRequest) -> str:
        """Generate a cache key for a request"""
        # Create a hash of the request components
        key_components = [
            request.user_input,
            request.language,
            request.system_prompt or "",
            self._hash_context(request.conversation_history),
        ]

        key_string = "|".join(str(comp) for comp in key_components)
        key_hash = hashlib.md5(key_string.encode()).hexdigest()

        return f"{self.cache_prefix}{key_hash}"

    def _hash_context(self, conversation_history: Optional[List[Any]]) -> str:
        """Generate hash for conversation context"""
        if not conversation_history:
            return ""

        # Create a simplified representation of conversation history
        context_items = []
        for turn in conversation_history[-5:]:  # Only last 5 turns for caching
            context_items.append(f"{turn.user_input}|{turn.agent_response}")

        context_string = "||".join(context_items)
        return hashlib.md5(context_string.encode()).hexdigest()

    async def _cleanup_cache_if_needed(self) -> None:
        """Clean up cache if it exceeds maximum size"""
        if self.cache_size >= self.max_cache_size:
            try:
                # Get all cache keys
                pattern = f"{self.cache_prefix}*"
                cache_keys = await self.storage.get_keys(pattern)

                if cache_keys:
                    # Remove oldest entries (simple FIFO approach)
                    keys_to_remove = cache_keys[: len(cache_keys) // 2]
                    for key in keys_to_remove:
                        await self.storage.delete(key)

                    self.cache_size = max(0, self.cache_size - len(keys_to_remove))
                    logger.debug("Cleaned up %d cache entries", len(keys_to_remove))

            except (AttributeError, TypeError, ValueError) as e:
                logger.error("Error during cache cleanup: %s", str(e))

    def get_cache_stats(self) -> dict:
        """Get cache statistics"""
        total_requests = self.cache_hits + self.cache_misses
        hit_rate = (self.cache_hits / total_requests * 100) if total_requests > 0 else 0

        return {
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "hit_rate": round(hit_rate, 2),
            "cache_size": self.cache_size,
            "max_cache_size": self.max_cache_size,
            "cache_enabled": self.cache_enabled,
        }
