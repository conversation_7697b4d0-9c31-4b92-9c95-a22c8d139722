"""
Groq LLM Provider for Lumina AI

Integrates with Groq's fast inference API for natural language processing.
"""

import asyncio
import json
import os
from typing import Dict, List, Optional, Any
from datetime import datetime

from groq import AsyncGroq

from llm.providers.base_provider import Base<PERSON><PERSON>rovider, LLMRequest, LLMResponse
from config.settings import get_settings
from utils.logging import get_logger

logger = get_logger(__name__)


class GroqProvider(BaseLLMProvider):
    """
    Groq LLM Provider implementation
    """

    def __init__(self, config: Dict[str, Any] = None):
        self.settings = get_settings()
        config = config or self.settings.llm.get("groq", {})
        super().__init__("groq", config)

        self.api_key = config.get("api_key") or os.getenv("GROQ_API_KEY")
        self.model_name = config.get("model", "llama3-8b-8192")
        self.max_tokens = config.get("max_tokens", 8192)
        self.temperature = config.get("temperature", 0.7)
        self.client = None

    async def _initialize_client(self) -> None:
        """Initialize Groq client"""
        if self.api_key:
            self.client = AsyncGroq(api_key=self.api_key)
            logger.info("Groq client initialized successfully")
        else:
            logger.warning("Groq API key not configured")
            raise ValueError("GROQ_API_KEY environment variable not set")

    async def generate(self, request: LLMRequest) -> LLMResponse:
        """Generate response using Groq API"""
        if not self.client:
            return LLMResponse(
                content="Groq provider not configured",
                success=False,
                error="API key not available",
                provider="groq",
                model=self.model_name,
                timestamp=datetime.now(),
            )

        try:
            # Prepare messages for chat completion
            messages = self._build_messages(request)

            # Generate response
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                stream=False,
            )

            if response.choices and response.choices[0].message:
                content = response.choices[0].message.content

                return LLMResponse(
                    content=content.strip() if content else "",
                    success=True,
                    provider="groq",
                    model=self.model_name,
                    timestamp=datetime.now(),
                    metadata={
                        "finish_reason": response.choices[0].finish_reason,
                        "usage": (
                            {
                                "prompt_tokens": (
                                    response.usage.prompt_tokens
                                    if response.usage
                                    else 0
                                ),
                                "completion_tokens": (
                                    response.usage.completion_tokens
                                    if response.usage
                                    else 0
                                ),
                                "total_tokens": (
                                    response.usage.total_tokens if response.usage else 0
                                ),
                            }
                            if response.usage
                            else {}
                        ),
                        "model": response.model,
                        "created": response.created,
                    },
                )

            return LLMResponse(
                content="No response generated",
                success=False,
                error="Empty response from Groq",
                provider="groq",
                model=self.model_name,
                timestamp=datetime.now(),
            )

        except Exception as e:
            logger.error("Groq API error: %s", e)
            return LLMResponse(
                content="",
                success=False,
                error=str(e),
                provider="groq",
                model=self.model_name,
                timestamp=datetime.now(),
            )

    def _build_messages(self, request: LLMRequest) -> List[Dict[str, str]]:
        """Build messages array for Groq chat completion"""
        messages = []

        # Add system message
        if request.system_prompt:
            messages.append({"role": "system", "content": request.system_prompt})

        # Add conversation history
        if request.conversation_history:
            for turn in request.conversation_history:
                messages.append({"role": "user", "content": turn.user_input})
                messages.append({"role": "assistant", "content": turn.agent_response})

        # Add current user input
        messages.append({"role": "user", "content": request.user_input})

        return messages

    async def check_health(self) -> bool:
        """Check if Groq API is accessible"""
        if not self.client:
            return False

        try:
            # Simple health check with minimal request
            test_request = LLMRequest(
                user_input="Hello", language="en", session_id="health_check"
            )

            response = await self.generate_response(test_request)
            return response.success

        except Exception as e:
            logger.error("Groq health check failed: %s", e)
            return False

    def get_provider_info(self) -> Dict[str, Any]:
        """Get provider information"""
        return {
            "name": "groq",
            "model": self.model_name,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "configured": self.client is not None,
            "supports_streaming": True,
            "supports_function_calling": False,
            "supported_languages": [
                "hi",
                "en",
                "mr",
                "gu",
                "ta",
                "te",
                "bn",
                "kn",
                "ml",
                "or",
                "pa",
                "as",
            ],
        }

    async def estimate_tokens(self, text: str) -> int:
        """Estimate token count for text"""
        # Rough estimation: 1 token ≈ 4 characters for most languages
        # Groq doesn't provide a direct token counting API
        return len(text) // 4

    def get_rate_limits(self) -> Dict[str, Any]:
        """Get rate limit information"""
        return {
            "requests_per_minute": 30,
            "tokens_per_minute": 14400,
            "requests_per_day": 14400,
        }

    async def generate_streaming_response(self, request: LLMRequest):
        """Generate streaming response using Groq API"""
        if not self.client:
            yield LLMResponse(
                content="Groq provider not configured",
                success=False,
                error="API key not available",
                provider="groq",
                model=self.model_name,
                timestamp=datetime.now(),
            )
            return

        try:
            # Prepare messages for chat completion
            messages = self._build_messages(request)

            # Generate streaming response
            stream = await self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                stream=True,
            )

            async for chunk in stream:
                if chunk.choices and chunk.choices[0].delta.content:
                    yield LLMResponse(
                        content=chunk.choices[0].delta.content,
                        success=True,
                        provider="groq",
                        model=self.model_name,
                        timestamp=datetime.now(),
                        metadata={
                            "chunk": True,
                            "finish_reason": chunk.choices[0].finish_reason,
                        },
                    )

        except Exception as e:
            logger.error("Groq streaming error: %s", e)
            yield LLMResponse(
                content="",
                success=False,
                error=str(e),
                provider="groq",
                model=self.model_name,
                timestamp=datetime.now(),
            )

    def is_available(self) -> bool:
        """Check if Groq provider is available"""
        return self._initialized and self.client is not None
