languages:
  hi:
    name: "Hindi"
    native_name: "हिंदी"
    code: "hi"
    locale: "hi-IN"
    rtl: false
    voice_config:
      tts_voice: "hi-IN-SwaraNeural"
      speech_rate: "+0%"
      speech_pitch: "+0Hz"
    prompts:
      greeting: "नमस्ते! मैं लुमिना हूँ। मैं आपकी कैसे सहायता कर सकती हूँ?"
      error: "क्षमा करें, मुझे कुछ समस्या हो रही है। कृपया दोबारा कोशिश करें।"
      not_understood: "मुझे खुशी होगी अगर आप अपना सवाल दोबारा पूछ सकें।"
      goodbye: "धन्यवाद! अच्छा दिन हो।"
    
  en:
    name: "English"
    native_name: "English"
    code: "en"
    locale: "en-IN"
    rtl: false
    voice_config:
      tts_voice: "en-US-AriaNeural"
      speech_rate: "+0%"
      speech_pitch: "+0Hz"
    prompts:
      greeting: "Hello! I'm <PERSON><PERSON>. How can I help you today?"
      error: "I'm sorry, I'm experiencing some issues. Please try again."
      not_understood: "I'd be happy to help if you could please repeat your question."
      goodbye: "Thank you! Have a great day."
    
  mr:
    name: "Marathi"
    native_name: "मराठी"
    code: "mr"
    locale: "mr-IN"
    rtl: false
    voice_config:
      tts_voice: "mr-IN-AarohiNeural"
      speech_rate: "+0%"
      speech_pitch: "+0Hz"
    prompts:
      greeting: "नमस्कार! मी लुमिना आहे। मी तुमची कशी मदत करू शकते?"
      error: "माफ करा, मला काही अडचण येत आहे। कृपया पुन्हा प्रयत्न करा."
      not_understood: "तुम्ही तुमचा प्रश्न पुन्हा विचारू शकाल का?"
      goodbye: "धन्यवाद! चांगला दिवस असो."
    
  gu:
    name: "Gujarati"
    native_name: "ગુજરાતી"
    code: "gu"
    locale: "gu-IN"
    rtl: false
    voice_config:
      tts_voice: "gu-IN-DhwaniNeural"
      speech_rate: "+0%"
      speech_pitch: "+0Hz"
    prompts:
      greeting: "નમસ્તે! હું લુમિના છું. હું તમારી કેવી રીતે મદદ કરી શકું?"
      error: "માફ કરશો, મને કંઈક સમસ્યા આવી રહી છે. કૃપા કરીને ફરીથી પ્રયાસ કરો."
      not_understood: "તમે તમારો પ્રશ્ન ફરીથી પૂછી શકશો?"
      goodbye: "આભાર! સારો દિવસ રહો."
    
  ta:
    name: "Tamil"
    native_name: "தமிழ்"
    code: "ta"
    locale: "ta-IN"
    rtl: false
    voice_config:
      tts_voice: "ta-IN-PallaviNeural"
      speech_rate: "+0%"
      speech_pitch: "+0Hz"
    prompts:
      greeting: "வணக்கம்! நான் லுமினா. உங்களுக்கு எப்படி உதவ முடியும்?"
      error: "மன்னிக்கவும், எனக்கு சில பிரச்சனைகள் உள்ளன. தயவுசெய்து மீண்டும் முயற்சிக்கவும்."
      not_understood: "உங்கள் கேள்வியை மீண்டும் கேட்க முடியுமா?"
      goodbye: "நன்றி! நல்ல நாள் இருக்கட்டும்."
    
  te:
    name: "Telugu"
    native_name: "తెలుగు"
    code: "te"
    locale: "te-IN"
    rtl: false
    voice_config:
      tts_voice: "te-IN-ShrutiNeural"
      speech_rate: "+0%"
      speech_pitch: "+0Hz"
    prompts:
      greeting: "నమస్కారం! నేను లుమినా. నేను మీకు ఎలా సహాయం చేయగలను?"
      error: "క్షమించండి, నాకు కొన్ని సమస్యలు వస్తున్నాయి. దయచేసి మళ్లీ ప్రయత్నించండి."
      not_understood: "మీరు మీ ప్రశ్నను మళ్లీ అడగగలరా?"
      goodbye: "ధన్యవాదాలు! మంచి రోజు గడపండి."

# Language detection patterns
detection_patterns:
  hi:
    keywords: ["नमस्ते", "धन्यवाद", "कैसे", "क्या", "कहाँ", "कब", "कौन", "मैं", "आप", "है"]
    script_ranges: [[0x0900, 0x097F]]  # Devanagari
  en:
    keywords: ["hello", "thank", "how", "what", "where", "when", "who", "i", "you", "is"]
    script_ranges: [[0x0041, 0x005A], [0x0061, 0x007A]]  # Latin
  mr:
    keywords: ["नमस्कार", "धन्यवाद", "कसे", "काय", "कुठे", "केव्हा", "कोण", "मी", "तुम्ही", "आहे"]
    script_ranges: [[0x0900, 0x097F]]  # Devanagari
  gu:
    keywords: ["નમસ્તે", "આભાર", "કેવી", "શું", "ક્યાં", "ક્યારે", "કોણ", "હું", "તમે", "છે"]
    script_ranges: [[0x0A80, 0x0AFF]]  # Gujarati
  ta:
    keywords: ["வணக்கம்", "நன்றி", "எப்படி", "என்ன", "எங்கே", "எப்போது", "யார்", "நான்", "நீங்கள்", "இருக்கிறது"]
    script_ranges: [[0x0B80, 0x0BFF]]  # Tamil
  te:
    keywords: ["నమస్కారం", "ధన్యవాదాలు", "ఎలా", "ఏమిటి", "ఎక్కడ", "ఎప్పుడు", "ఎవరు", "నేను", "మీరు", "ఉంది"]
    script_ranges: [[0x0C00, 0x0C7F]]  # Telugu
