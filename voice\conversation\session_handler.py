"""
Voice Conversation Session Handler for Lumina AI
Manages complete voice conversation sessions with STT → LLM → TTS pipeline
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime

from voice.conversation.context_manager import ConversationContextManager, ConversationState
from voice.conversation.voice_commands import VoiceCommandR<PERSON>ognizer, CommandMatch
from utils.logging import get_logger

logger = get_logger(__name__)


class VoiceSessionHandler:
    """Handles complete voice conversation sessions"""
    
    def __init__(self, voice_coordinator, llm_router, agent_router):
        self.logger = logging.getLogger(__name__)
        
        # Core components
        self.voice_coordinator = voice_coordinator
        self.llm_router = llm_router
        self.agent_router = agent_router
        
        # Conversation management
        self.context_manager = ConversationContextManager()
        self.command_recognizer = VoiceCommandRecognizer()
        
        # Session state
        self.is_active = False
        self.last_response = ""
        self.processing_queue = asyncio.Queue()
        
        # Configuration
        self.response_timeout = 30.0  # seconds
        self.min_confidence_threshold = 0.7
        self.max_response_length = 500  # characters
        
        # Register command handlers
        self._register_command_handlers()
        
        # Performance tracking
        self.session_metrics = {
            "total_interactions": 0,
            "successful_responses": 0,
            "command_executions": 0,
            "average_response_time": 0.0
        }
    
    def _register_command_handlers(self):
        """Register handlers for voice commands"""
        
        self.command_recognizer.register_handler("handle_stop_listening", self._handle_stop_listening)
        self.command_recognizer.register_handler("handle_start_listening", self._handle_start_listening)
        self.command_recognizer.register_handler("handle_get_time", self._handle_get_time)
        self.command_recognizer.register_handler("handle_get_date", self._handle_get_date)
        self.command_recognizer.register_handler("handle_repeat_response", self._handle_repeat_response)
        self.command_recognizer.register_handler("handle_change_language", self._handle_change_language)
        self.command_recognizer.register_handler("handle_volume_control", self._handle_volume_control)
        self.command_recognizer.register_handler("handle_help", self._handle_help)
    
    async def start_session(self) -> bool:
        """Start a new voice conversation session"""
        try:
            if self.is_active:
                self.logger.warning("Session already active")
                return True
            
            # Start conversation context
            session_id = await self.context_manager.start_session()
            
            # Update state
            self.is_active = True
            self.context_manager.update_session_state(ConversationState.LISTENING)
            
            self.logger.info(f"🎯 Voice session started: {session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start voice session: {e}")
            return False
    
    async def end_session(self) -> bool:
        """End the current voice conversation session"""
        try:
            if not self.is_active:
                return True
            
            # End conversation context
            await self.context_manager.end_session()
            
            # Update state
            self.is_active = False
            
            # Log session summary
            summary = self.context_manager.get_session_summary()
            self.logger.info(f"🎯 Voice session ended: {summary}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to end voice session: {e}")
            return False
    
    async def process_voice_input(self, audio_data: bytes, language: str = "hi") -> Dict[str, Any]:
        """
        Process voice input through the complete STT → LLM → TTS pipeline
        
        Args:
            audio_data: Raw audio data from microphone
            language: Expected language of the input
            
        Returns:
            Processing result with response and metadata
        """
        start_time = time.time()
        
        try:
            if not self.is_active:
                await self.start_session()
            
            self.context_manager.update_session_state(ConversationState.PROCESSING)
            
            # Step 1: Speech-to-Text
            self.logger.info("🎤 Processing voice input: STT...")
            stt_result = await self.voice_coordinator.transcribe_audio(audio_data, language)
            
            if not stt_result.get("success", False):
                return {
                    "success": False,
                    "error": "Speech recognition failed",
                    "response": "Sorry, I couldn't understand what you said."
                }
            
            transcribed_text = stt_result.get("text", "").strip()
            confidence = stt_result.get("confidence", 0.0)
            
            if not transcribed_text or confidence < self.min_confidence_threshold:
                return {
                    "success": False,
                    "error": "Low confidence transcription",
                    "response": "Could you please repeat that more clearly?"
                }
            
            self.logger.info(f"🎤 Transcribed: '{transcribed_text}' (confidence: {confidence:.2f})")
            
            # Step 2: Check for voice commands
            command_match = await self.command_recognizer.recognize_command(transcribed_text, language)
            
            if command_match:
                self.logger.info(f"🎯 Executing voice command: {command_match.command.command_id}")
                command_result = await self.command_recognizer.execute_command(command_match)
                
                # Update metrics
                self.session_metrics["command_executions"] += 1
                
                # Generate voice response for command
                if command_result.get("success", False):
                    response_text = command_result.get("response", "Command executed successfully.")
                    await self._generate_voice_response(response_text, language)
                
                return command_result
            
            # Step 3: Process through LLM
            self.logger.info("🤖 Processing through LLM...")
            
            # Get conversation context
            context = self.context_manager.get_conversation_context()
            
            # Prepare LLM request
            llm_request = {
                "message": transcribed_text,
                "language": language,
                "context": context,
                "max_length": self.max_response_length
            }
            
            # Route to appropriate agent or use general conversation
            if self.agent_router.agents:
                llm_response = await self.agent_router.route_request(llm_request)
            else:
                llm_response = await self.llm_router.generate_response(
                    prompt=transcribed_text,
                    context=context,
                    language=language
                )
            
            if not llm_response.get("success", False):
                return {
                    "success": False,
                    "error": "LLM processing failed",
                    "response": "Sorry, I'm having trouble processing your request right now."
                }
            
            response_text = llm_response.get("response", "").strip()
            
            # Step 4: Text-to-Speech
            await self._generate_voice_response(response_text, language)
            
            # Step 5: Update conversation context
            processing_time = time.time() - start_time
            
            await self.context_manager.add_conversation_turn(
                user_input=transcribed_text,
                ai_response=response_text,
                user_language=language,
                response_language=language,
                confidence=confidence,
                processing_time=processing_time
            )
            
            # Update metrics
            self.session_metrics["total_interactions"] += 1
            self.session_metrics["successful_responses"] += 1
            self._update_average_response_time(processing_time)
            
            # Update state
            self.context_manager.update_session_state(ConversationState.LISTENING)
            
            self.logger.info(f"✅ Voice interaction completed in {processing_time:.2f}s")
            
            return {
                "success": True,
                "transcribed_text": transcribed_text,
                "response_text": response_text,
                "confidence": confidence,
                "processing_time": processing_time,
                "language": language
            }
            
        except Exception as e:
            self.logger.error(f"Error processing voice input: {e}")
            
            # Try to provide error response via TTS
            error_response = "Sorry, there was an error processing your request."
            await self._generate_voice_response(error_response, language)
            
            return {
                "success": False,
                "error": str(e),
                "response": error_response
            }
    
    async def _generate_voice_response(self, text: str, language: str = "hi"):
        """Generate and play voice response"""
        try:
            self.context_manager.update_session_state(ConversationState.RESPONDING)
            
            self.logger.info(f"🎤 Generating voice response: '{text[:50]}...'")
            
            # Generate TTS
            tts_result = await self.voice_coordinator.synthesize_speech(text, language)
            
            if tts_result.get("success", False):
                # Play audio
                audio_data = tts_result.get("audio_data")
                if audio_data:
                    await self.voice_coordinator.play_audio(audio_data)
                    self.last_response = text
                    self.logger.info("🎤 Voice response played successfully")
                else:
                    self.logger.warning("No audio data received from TTS")
            else:
                self.logger.error(f"TTS failed: {tts_result.get('error', 'Unknown error')}")
                
        except Exception as e:
            self.logger.error(f"Error generating voice response: {e}")
    
    def _update_average_response_time(self, response_time: float):
        """Update average response time metric"""
        current_avg = self.session_metrics["average_response_time"]
        total_responses = self.session_metrics["successful_responses"]
        
        if total_responses == 1:
            self.session_metrics["average_response_time"] = response_time
        else:
            # Calculate running average
            new_avg = ((current_avg * (total_responses - 1)) + response_time) / total_responses
            self.session_metrics["average_response_time"] = new_avg
    
    # Command Handlers
    async def _handle_stop_listening(self, command_match: CommandMatch) -> Dict[str, Any]:
        """Handle stop listening command"""
        await self.end_session()
        return {
            "success": True,
            "response": "Goodbye! Voice interaction stopped." if command_match.language == "en" else "अलविदा! आवाज़ बातचीत बंद की गई।"
        }
    
    async def _handle_start_listening(self, command_match: CommandMatch) -> Dict[str, Any]:
        """Handle start listening command"""
        await self.start_session()
        return {
            "success": True,
            "response": "Hello! I'm listening." if command_match.language == "en" else "नमस्ते! मैं सुन रहा हूं।"
        }
    
    async def _handle_get_time(self, command_match: CommandMatch) -> Dict[str, Any]:
        """Handle get time command"""
        current_time = datetime.now().strftime("%I:%M %p")
        
        if command_match.language == "hi":
            response = f"अभी समय {current_time} है।"
        else:
            response = f"The current time is {current_time}."
        
        return {"success": True, "response": response}
    
    async def _handle_get_date(self, command_match: CommandMatch) -> Dict[str, Any]:
        """Handle get date command"""
        current_date = datetime.now().strftime("%B %d, %Y")
        
        if command_match.language == "hi":
            response = f"आज की तारीख {current_date} है।"
        else:
            response = f"Today's date is {current_date}."
        
        return {"success": True, "response": response}
    
    async def _handle_repeat_response(self, command_match: CommandMatch) -> Dict[str, Any]:
        """Handle repeat response command"""
        if self.last_response:
            return {"success": True, "response": self.last_response}
        else:
            response = "I haven't said anything yet." if command_match.language == "en" else "मैंने अभी तक कुछ नहीं कहा है।"
            return {"success": True, "response": response}
    
    async def _handle_change_language(self, command_match: CommandMatch) -> Dict[str, Any]:
        """Handle change language command"""
        new_language = command_match.extracted_params.get("language", "hi")
        
        # Update context manager language
        if self.context_manager.current_session:
            self.context_manager.current_session.primary_language = new_language
        
        if new_language == "hi":
            response = "भाषा हिंदी में बदल दी गई है।"
        else:
            response = f"Language changed to {new_language}."
        
        return {"success": True, "response": response}
    
    async def _handle_volume_control(self, command_match: CommandMatch) -> Dict[str, Any]:
        """Handle volume control command"""
        action = command_match.extracted_params.get("action", "increase")
        
        # This would integrate with actual volume control
        if action == "increase":
            response = "Volume increased." if command_match.language == "en" else "आवाज़ बढ़ाई गई।"
        else:
            response = "Volume decreased." if command_match.language == "en" else "आवाज़ कम की गई।"
        
        return {"success": True, "response": response}
    
    async def _handle_help(self, command_match: CommandMatch) -> Dict[str, Any]:
        """Handle help command"""
        if command_match.language == "hi":
            response = "मैं आपकी बात सुन सकता हूं, सवालों के जवाब दे सकता हूं, समय और तारीख बता सकता हूं। आप मुझसे कुछ भी पूछ सकते हैं।"
        else:
            response = "I can listen to you, answer questions, tell time and date. You can ask me anything!"
        
        return {"success": True, "response": response}
    
    def get_status(self) -> Dict[str, Any]:
        """Get session handler status"""
        return {
            "is_active": self.is_active,
            "context_manager": self.context_manager.get_status(),
            "command_recognizer": self.command_recognizer.get_status(),
            "session_metrics": self.session_metrics,
            "last_response": self.last_response[:100] if self.last_response else None
        }
