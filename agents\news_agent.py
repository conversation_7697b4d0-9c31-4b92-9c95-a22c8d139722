"""
News Agent for Lumina AI

Provides current news and headlines.
"""

import asyncio
import json
from typing import Dict, List, Optional, Any
from datetime import datetime

from agents.base.agent_interface import BaseAgent, AgentRequest, AgentResponse, AgentCapability
from config.settings import get_settings
from utils.logging import get_logger

logger = get_logger(__name__)


class NewsAgent(BaseAgent):
    """
    Agent for handling news-related queries
    """
    
    def __init__(self):
        super().__init__()
        self.settings = get_settings()
        self.name = "news"
        self.description = "Provides current news and headlines"
        self.version = "1.0.0"
        
        # News API configuration
        self.api_key = self.settings.agents.get("news", {}).get("api_key")
        self.api_url = "https://newsapi.org/v2"
        
        # Supported capabilities
        self.capabilities = [
            AgentCapability.INFORMATION_RETRIEVAL,
            AgentCapability.REAL_TIME_DATA
        ]
        
        # Keywords for intent detection
        self.keywords = {
            "hi": ["समाचार", "न्यूज़", "खबर", "ताजा", "हेडलाइन", "अपडेट"],
            "en": ["news", "headlines", "current", "latest", "breaking", "update"],
            "mr": ["बातमी", "न्यूज", "ताजी", "हेडलाइन"],
            "gu": ["સમાચાર", "ન્યૂઝ", "તાજા", "હેડલાઇન"],
            "ta": ["செய்தி", "புதிய", "தலைப்புச் செய்தி"],
            "te": ["వార్తలు", "కొత్త", "హెడ్‌లైన్స్"]
        }
        
        # News categories
        self.categories = {
            "hi": {
                "सामान्य": "general",
                "व्यापार": "business",
                "मनोरंजन": "entertainment",
                "स्वास्थ्य": "health",
                "विज्ञान": "science",
                "खेल": "sports",
                "तकनीक": "technology"
            },
            "en": {
                "general": "general",
                "business": "business",
                "entertainment": "entertainment",
                "health": "health",
                "science": "science",
                "sports": "sports",
                "technology": "technology"
            }
        }
    
    async def initialize(self) -> bool:
        """Initialize the news agent"""
        logger.info("Initializing News Agent")
        
        if not self.api_key:
            logger.warning("News API key not configured")
            # Continue without API for demo purposes
        
        try:
            logger.info("News agent initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize news agent: {str(e)}")
            return False
    
    async def process_request(self, request: AgentRequest) -> AgentResponse:
        """Process a news-related request"""
        try:
            user_input = request.user_input.lower()
            language = request.language
            
            # Extract category and location
            category = self._extract_category(user_input, language)
            location = self._extract_location(user_input, language)
            
            # Get news data
            news_data = await self._get_news_data(category, location, language)
            
            if not news_data:
                return AgentResponse(
                    content=self._get_error_message(language),
                    success=False,
                    agent_name=self.name,
                    timestamp=datetime.now()
                )
            
            # Format response
            response_text = self._format_news_response(news_data, language)
            
            return AgentResponse(
                content=response_text,
                success=True,
                agent_name=self.name,
                timestamp=datetime.now(),
                metadata={
                    "category": category,
                    "location": location,
                    "news_count": len(news_data)
                }
            )
            
        except Exception as e:
            logger.error(f"Error processing news request: {str(e)}")
            return AgentResponse(
                content=self._get_error_message(request.language),
                success=False,
                agent_name=self.name,
                timestamp=datetime.now(),
                error=str(e)
            )
    
    def can_handle_request(self, request: AgentRequest) -> float:
        """Check if this agent can handle the request"""
        user_input = request.user_input.lower()
        language = request.language
        
        # Check for news-related keywords
        keywords = self.keywords.get(language, self.keywords["en"])
        
        for keyword in keywords:
            if keyword in user_input:
                return 0.8  # High confidence for news keywords
        
        return 0.0
    
    def _extract_category(self, user_input: str, language: str) -> str:
        """Extract news category from user input"""
        categories = self.categories.get(language, self.categories["en"])
        
        for category_name, category_code in categories.items():
            if category_name in user_input:
                return category_code
        
        return "general"  # Default category
    
    def _extract_location(self, user_input: str, language: str) -> Optional[str]:
        """Extract location from user input"""
        # Simple location extraction - in practice, use NLP
        location_keywords = {
            "hi": ["भारत", "दिल्ली", "मुंबई", "कोलकाता", "चेन्नई", "बैंगलोर"],
            "en": ["india", "delhi", "mumbai", "kolkata", "chennai", "bangalore", "us", "usa"]
        }
        
        keywords = location_keywords.get(language, location_keywords["en"])
        
        for keyword in keywords:
            if keyword in user_input:
                return keyword
        
        # Default to India for Indian languages
        if language in ["hi", "mr", "gu", "ta", "te"]:
            return "in"  # India country code
        
        return None
    
    async def _get_news_data(self, category: str, location: Optional[str], language: str) -> Optional[List[Dict[str, Any]]]:
        """Get news data from API"""
        if not self.api_key:
            # Return mock data for demo
            return self._get_mock_news_data(category, language)
        
        try:
            # TODO: Implement actual API call
            # For now, return mock data
            return self._get_mock_news_data(category, language)
        except Exception as e:
            logger.error(f"Error fetching news data: {str(e)}")
            return None
    
    def _get_mock_news_data(self, category: str, language: str) -> List[Dict[str, Any]]:
        """Get mock news data for demo purposes"""
        mock_news = {
            "hi": [
                {
                    "title": "आज की मुख्य खबरें",
                    "description": "देश और दुनिया की ताजा खबरें",
                    "source": "न्यूज़ एजेंसी",
                    "publishedAt": datetime.now().isoformat()
                },
                {
                    "title": "तकनीकी अपडेट",
                    "description": "नई तकनीक की जानकारी",
                    "source": "टेक न्यूज़",
                    "publishedAt": datetime.now().isoformat()
                }
            ],
            "en": [
                {
                    "title": "Today's Top Headlines",
                    "description": "Latest news from around the world",
                    "source": "News Agency",
                    "publishedAt": datetime.now().isoformat()
                },
                {
                    "title": "Technology Update",
                    "description": "Latest technology developments",
                    "source": "Tech News",
                    "publishedAt": datetime.now().isoformat()
                }
            ]
        }
        
        return mock_news.get(language, mock_news["en"])
    
    def _format_news_response(self, news_data: List[Dict[str, Any]], language: str) -> str:
        """Format news data into a response"""
        if not news_data:
            return self._get_no_news_message(language)
        
        response_parts = []
        
        # Add header
        headers = {
            "hi": "📰 ताजा समाचार:",
            "en": "📰 Latest News:",
            "mr": "📰 ताजी बातमी:",
            "gu": "📰 તાજા સમાચાર:",
            "ta": "📰 சமீபத்திய செய்திகள்:",
            "te": "📰 తాజా వార్తలు:"
        }
        
        response_parts.append(headers.get(language, headers["en"]))
        response_parts.append("")
        
        # Add news items
        for i, news_item in enumerate(news_data[:5], 1):  # Limit to 5 news items
            title = news_item.get("title", "")
            description = news_item.get("description", "")
            source = news_item.get("source", "")
            
            news_text = f"{i}. {title}"
            if description:
                news_text += f"\n   {description}"
            if source:
                news_text += f"\n   📍 {source}"
            
            response_parts.append(news_text)
            response_parts.append("")
        
        return "\n".join(response_parts)
    
    def _get_no_news_message(self, language: str) -> str:
        """Get no news available message"""
        messages = {
            "hi": "इस समय कोई समाचार उपलब्ध नहीं है।",
            "en": "No news available at the moment.",
            "mr": "सध्या कोणतीही बातमी उपलब्ध नाही.",
            "gu": "આ સમયે કોઈ સમાચાર ઉપલબ્ધ નથી.",
            "ta": "இந்த நேரத்தில் எந்த செய்தியும் கிடைக்கவில்லை.",
            "te": "ప్రస్తుతం ఎలాంటి వార్తలు అందుబాటులో లేవు."
        }
        
        return messages.get(language, messages["en"])
    
    def _get_error_message(self, language: str) -> str:
        """Get error message"""
        messages = {
            "hi": "समाचार प्राप्त करने में समस्या हो रही है। कृपया बाद में कोशिश करें।",
            "en": "I'm having trouble getting news. Please try again later.",
            "mr": "बातमी मिळवण्यात अडचण येत आहे. कृपया नंतर प्रयत्न करा.",
            "gu": "સમાચાર મેળવવામાં મુશ્કેલી આવી રહી છે. કૃપા કરીને પછીથી પ્રયાસ કરો.",
            "ta": "செய்திகளைப் பெறுவதில் சிக்கல் உள்ளது. பின்னர் முயற்சிக்கவும்.",
            "te": "వార్తలు పొందడంలో ఇబ్బంది ఉంది. దయచేసి తర్వాత ప్రయత్నించండి."
        }
        
        return messages.get(language, messages["en"])
