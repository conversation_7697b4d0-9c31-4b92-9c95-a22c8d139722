# Core dependencies
asyncio
pydantic>=2.0.0
python-dotenv>=1.0.0
pyyaml>=6.0

# Voice processing
livekit>=0.10.0
openai-whisper>=20231117
edge-tts>=6.1.0
pyaudio>=0.2.11
speechrecognition>=3.10.0

# LLM providers
google-generativeai>=0.3.0
groq>=0.4.0
openai>=1.0.0
anthropic>=0.7.0

# Azure services (optional)
azure-cognitiveservices-speech>=1.30.0
azure-ai-textanalytics>=5.3.0

# Web framework and APIs
fastapi>=0.104.0
uvicorn>=0.24.0
websockets>=12.0
requests>=2.31.0
httpx>=0.25.0

# Data processing and storage
redis>=5.0.0
sqlalchemy>=2.0.0
alembic>=1.12.0
pandas>=2.1.0

# Monitoring and logging
prometheus-client>=0.19.0
structlog>=23.2.0
psutil>=5.9.0

# Security and encryption
cryptography>=41.0.0
python-jose>=3.3.0
passlib>=1.7.4

# Language processing
indic-nlp-library>=0.81
# polyglot>=16.7.4  # Commented out due to encoding issues on Windows
spacy>=3.7.0
langdetect>=1.0.9
nltk>=3.8.1

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.12.0
coverage>=7.3.0

# Development tools
black>=23.9.0
flake8>=6.1.0
mypy>=1.6.0
pre-commit>=3.5.0

# Utilities
click>=8.1.0
rich>=13.6.0
tqdm>=4.66.0
