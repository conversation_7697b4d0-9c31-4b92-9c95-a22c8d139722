"""
Greeting System for Lumina AI
Handles automatic voice greetings after initialization
"""

import asyncio
import logging
import random
from typing import Optional, Dict, Any, List
from datetime import datetime

from voice.voice_coordinator import VoiceCoordinator
from voice.output.audio_player import AudioPlayer
from config.settings import get_settings


class GreetingSystem:
    """Manages automatic voice greetings for Lumina AI"""

    def __init__(self, voice_coordinator: VoiceCoordinator, audio_player: AudioPlayer):
        self.voice_coordinator = voice_coordinator
        self.audio_player = audio_player
        self.logger = logging.getLogger(__name__)
        self.settings = get_settings()

        # Greeting configuration
        self.default_language = self.settings.app.get("default_language", "hi")
        self.greeting_enabled = True
        self.greeting_delay = 1.0  # Delay after initialization before greeting
        self.greeting_speech_rate = "fast"  # Faster speech rate for greetings
        self.greeting_speech_volume = "medium"  # Standard volume for greetings

        # Greeting messages in different languages
        self.greeting_messages = {
            "hi": [
                "नमस्ते! मैं लुमिना एआई हूं, आपका आवाज सहायक। मैं आपकी सहायता के लिए तैयार हूं।",
                "स्वागत है! मैं लुमिना एआई हूं। आप मुझसे हिंदी या अंग्रेजी में बात कर सकते हैं।",
                "हैलो! मैं लुमिना एआई हूं। मैं यहां आपकी मदद करने के लिए हूं।",
            ],
            "en": [
                "Hello! I'm Lumina AI, your voice assistant. I'm ready to help you.",
                "Welcome! I'm Lumina AI. You can speak to me in Hindi or English.",
                "Hi there! I'm Lumina AI, and I'm here to assist you with anything you need.",
            ],
            "mr": [
                "नमस्कार! मी लुमिना एआय आहे, तुमचा आवाज सहाय्यक। मी तुमची मदत करण्यासाठी तयार आहे।",
                "स्वागत आहे! मी लुमिना एआय आहे। तुम्ही माझ्याशी मराठी किंवा इंग्रजीत बोलू शकता।",
            ],
            "gu": [
                "નમસ્તે! હું લુમિના એઆઈ છું, તમારો અવાજ સહાયક। હું તમારી મદદ કરવા માટે તૈયાર છું।",
                "સ્વાગત છે! હું લુમિના એઆઈ છું। તમે મારી સાથે ગુજરાતી અથવા અંગ્રેજીમાં વાત કરી શકો છો।",
            ],
            "ta": [
                "வணக்கம்! நான் லுமினா AI, உங்கள் குரல் உதவியாளர். நான் உங்களுக்கு உதவ தயாராக இருக்கிறேன்।",
                "வரவேற்கிறோம்! நான் லுமினா AI. நீங்கள் என்னிடம் தமிழ் அல்லது ஆங்கிலத்தில் பேசலாம்।",
            ],
            "te": [
                "నమస్కారం! నేను లుమినా AI, మీ వాయిస్ అసిస్టెంట్. నేను మీకు సహాయం చేయడానికి సిద్ధంగా ఉన్నాను।",
                "స్వాగతం! నేను లుమినా AI. మీరు నాతో తెలుగు లేదా ఇంగ్లీష్‌లో మాట్లాడవచ్చు।",
            ],
        }

        # Time-based greetings
        self.time_based_greetings = {
            "hi": {
                "morning": "सुप्रभात! मैं लुमिना एआई हूं। आज आपकी कैसे मदद कर सकती हूं?",
                "afternoon": "नमस्ते! मैं लुमिना एआई हूं। आज दोपहर आपकी कैसे सहायता कर सकती हूं?",
                "evening": "शुभ संध्या! मैं लुमिना एआई हूं। आज शाम आपकी कैसे मदद कर सकती हूं?",
                "night": "नमस्ते! मैं लुमिना एआई हूं। रात में भी आपकी सेवा में हूं।",
            },
            "en": {
                "morning": "Good morning! I'm Lumina AI. How can I help you today?",
                "afternoon": "Good afternoon! I'm Lumina AI. How can I assist you this afternoon?",
                "evening": "Good evening! I'm Lumina AI. How can I help you this evening?",
                "night": "Hello! I'm Lumina AI. I'm here to help you even at night.",
            },
        }

    def _get_time_of_day(self) -> str:
        """Determine time of day for appropriate greeting"""
        current_hour = datetime.now().hour

        if 5 <= current_hour < 12:
            return "morning"
        elif 12 <= current_hour < 17:
            return "afternoon"
        elif 17 <= current_hour < 21:
            return "evening"
        else:
            return "night"

    def _select_greeting_message(
        self, language: str = None, use_time_based: bool = True
    ) -> str:
        """Select an appropriate greeting message"""
        try:
            target_language = language or self.default_language

            # Use time-based greeting if available and requested
            if use_time_based and target_language in self.time_based_greetings:
                time_of_day = self._get_time_of_day()
                time_greetings = self.time_based_greetings[target_language]

                if time_of_day in time_greetings:
                    return time_greetings[time_of_day]

            # Fall back to regular greetings
            if target_language in self.greeting_messages:
                messages = self.greeting_messages[target_language]
                return random.choice(messages)

            # Ultimate fallback to English
            if "en" in self.greeting_messages:
                messages = self.greeting_messages["en"]
                return random.choice(messages)

            # Hard-coded fallback
            return "Hello! I'm Lumina AI, your voice assistant. I'm ready to help you."

        except Exception as e:
            self.logger.error(f"Error selecting greeting message: {e}")
            return "Hello! I'm Lumina AI, your voice assistant. I'm ready to help you."

    async def play_startup_greeting(
        self, language: str = None, custom_message: str = None
    ) -> bool:
        """
        Play automatic startup greeting

        Args:
            language: Language for greeting (optional)
            custom_message: Custom greeting message (optional)

        Returns:
            True if greeting was played successfully
        """
        try:
            if not self.greeting_enabled:
                self.logger.info("Greeting system disabled")
                return False

            if not self.voice_coordinator or not self.audio_player:
                self.logger.error("Voice coordinator or audio player not available")
                return False

            # Wait for initialization delay
            if self.greeting_delay > 0:
                await asyncio.sleep(self.greeting_delay)

            # Select greeting message
            if custom_message:
                greeting_text = custom_message
                target_language = language or self.default_language
            else:
                target_language = language or self.default_language
                greeting_text = self._select_greeting_message(target_language)

            self.logger.info(f"🎵 Playing startup greeting in {target_language}")
            self.logger.info(f"💬 Greeting: {greeting_text}")

            # Generate TTS audio with faster speech rate
            tts_result = await self.voice_coordinator.text_to_speech(
                text=greeting_text,
                language=target_language,
                speech_rate=self.greeting_speech_rate,
                speech_volume=self.greeting_speech_volume,
            )

            if not tts_result.success or not tts_result.audio_data:
                self.logger.error("Failed to generate greeting audio")
                return False

            # Play the audio
            playback_success = await self.audio_player.play_audio(
                audio_data=tts_result.audio_data, audio_format=tts_result.format
            )

            if playback_success:
                self.logger.info("✅ Startup greeting played successfully")

                # Wait for playback to complete
                max_wait_time = max(
                    10.0, tts_result.duration + 2.0
                )  # Duration + buffer
                wait_time = 0.0

                while (
                    self.audio_player.is_playback_active() and wait_time < max_wait_time
                ):
                    await asyncio.sleep(0.1)
                    wait_time += 0.1

                self.logger.info("🎵 Greeting playback completed")
                return True
            else:
                self.logger.error("Failed to play greeting audio")
                return False

        except Exception as e:
            self.logger.error(f"Error playing startup greeting: {e}")
            return False

    async def play_custom_greeting(self, message: str, language: str = None) -> bool:
        """
        Play a custom greeting message

        Args:
            message: Custom message to speak
            language: Language for TTS

        Returns:
            True if successful
        """
        return await self.play_startup_greeting(
            language=language, custom_message=message
        )

    def set_greeting_enabled(self, enabled: bool):
        """Enable or disable greeting system"""
        self.greeting_enabled = enabled
        self.logger.info(f"Greeting system {'enabled' if enabled else 'disabled'}")

    def set_greeting_delay(self, delay_seconds: float):
        """Set delay before playing greeting"""
        self.greeting_delay = max(0.0, delay_seconds)
        self.logger.info(f"Greeting delay set to {self.greeting_delay} seconds")

    def set_greeting_speech_rate(self, speech_rate: str):
        """Set speech rate for greetings"""
        valid_rates = ["x-slow", "slow", "medium", "fast", "x-fast"]
        if speech_rate in valid_rates:
            self.greeting_speech_rate = speech_rate
            self.logger.info(f"Greeting speech rate set to {speech_rate}")
        else:
            self.logger.warning(
                f"Invalid speech rate '{speech_rate}'. Valid options: {valid_rates}"
            )

    def set_greeting_speech_volume(self, speech_volume: str):
        """Set speech volume for greetings"""
        valid_volumes = ["x-soft", "soft", "medium", "loud", "x-loud"]
        if speech_volume in valid_volumes:
            self.greeting_speech_volume = speech_volume
            self.logger.info(f"Greeting speech volume set to {speech_volume}")
        else:
            self.logger.warning(
                f"Invalid speech volume '{speech_volume}'. Valid options: {valid_volumes}"
            )

    def add_custom_greeting(self, language: str, message: str):
        """Add a custom greeting message for a language"""
        if language not in self.greeting_messages:
            self.greeting_messages[language] = []

        self.greeting_messages[language].append(message)
        self.logger.info(f"Added custom greeting for {language}")

    def get_available_languages(self) -> List[str]:
        """Get list of languages with greeting messages"""
        return list(self.greeting_messages.keys())

    def get_status(self) -> Dict[str, Any]:
        """Get greeting system status"""
        return {
            "enabled": self.greeting_enabled,
            "delay_seconds": self.greeting_delay,
            "default_language": self.default_language,
            "available_languages": self.get_available_languages(),
            "voice_coordinator_available": self.voice_coordinator is not None,
            "audio_player_available": self.audio_player is not None,
        }
