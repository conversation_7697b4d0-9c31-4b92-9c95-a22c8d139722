# Lumina AI Environment Variables
# Copy this file to .env and fill in your actual API keys and configuration

# LLM Provider API Keys
GEMINI_API_KEY=AIzaSyCUjBRRVwUT2ixJ5jW42hb7JxSrw2IlMgk
GROQ_API_KEY=********************************************************
OPENROUTER_API_KEY=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

# Agent API Keys
WEATHER_API_KEY=1b4963dca806db0de026d869024eac59
NEWS_API_KEY=c79a2f80380746d2a01b0a8affe58c78
TRANSLATE_API_KEY=your_google_translate_api_key_here

# LiveKit Configuration
LIVEKIT_SERVER_URL=wss://lumina-mynu7cun.livekit.cloud
LIVEKIT_API_KEY=APIjPy7NXxocytN
LIVEKIT_API_SECRET=vk1WV8CDXQpvLc8hDM8OXKOgZGgDEK1o7VsrPPu72vG

# Security Keys
ENCRYPTION_KEY=your_32_byte_encryption_key_here
SESSION_SECRET=your_session_secret_key_here

# Database Configuration (if using)
DATABASE_URL=postgresql://user:password@localhost:5432/lumina_ai
REDIS_URL=redis://localhost:6379/0

# Azure Services (optional)
AZURE_SPEECH_KEY=your_azure_speech_key
AZURE_SPEECH_REGION=your_azure_region

# Application Configuration
DEBUG=false
LOG_LEVEL=INFO
MAX_CONCURRENT_REQUESTS=10

# Performance Tuning
CACHE_TTL=3600
REQUEST_TIMEOUT=30
VOICE_PROCESSING_TIMEOUT=5