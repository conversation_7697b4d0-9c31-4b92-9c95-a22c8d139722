"""
REST API endpoints for Lumina AI
"""

import asyncio
import json
import io
from typing import Dict, Any, Optional, List
from datetime import datetime
import uuid

try:
    from fastapi import FastAPI, HTTPException, UploadFile, File, Form, Depends
    from fastapi.responses import JSONResponse, StreamingResponse
    from fastapi.middleware.cors import CORSMiddleware
    from pydantic import BaseModel

    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False

from core.app import LuminaApp
from agents.base.agent_interface import AgentRequest, AgentResponse
from config.settings import get_settings
from utils.logging import get_logger

logger = get_logger(__name__)


# Pydantic models for API
class ChatRequest(BaseModel):
    message: str
    language: str = "hi"
    session_id: Optional[str] = None


class ChatResponse(BaseModel):
    response: str
    session_id: str
    language: str
    agent_used: str
    confidence: float
    processing_time: float
    success: bool
    error: Optional[str] = None


class VoiceRequest(BaseModel):
    language: str = "hi"
    session_id: Optional[str] = None


class VoiceResponse(BaseModel):
    transcription: str
    response: str
    session_id: str
    language: str
    agent_used: str
    confidence: float
    processing_time: float
    success: bool
    error: Optional[str] = None


class HealthResponse(BaseModel):
    status: str
    timestamp: str
    components: Dict[str, Any]


class LuminaAPI:
    """REST API for Lumina AI"""

    def __init__(self, lumina_app: LuminaApp):
        self.lumina_app = lumina_app
        self.settings = get_settings()

        if not FASTAPI_AVAILABLE:
            raise ImportError(
                "FastAPI not available. Install with: pip install fastapi uvicorn"
            )

        # Create FastAPI app
        self.app = FastAPI(
            title="Lumina AI API",
            description="REST API for Lumina AI Voice Assistant",
            version="1.0.0",
        )

        # Add CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],  # Configure appropriately for production
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

        # Setup routes
        self._setup_routes()

    def _setup_routes(self):
        """Setup API routes"""

        @self.app.get("/", response_model=Dict[str, str])
        async def root():
            """Root endpoint"""
            return {
                "service": "Lumina AI",
                "version": "1.0.0",
                "status": "running",
                "timestamp": datetime.now().isoformat(),
            }

        @self.app.post("/chat", response_model=ChatResponse)
        async def chat(request: ChatRequest):
            """Text-based chat endpoint"""
            try:
                start_time = datetime.now()

                # Generate session ID if not provided
                session_id = request.session_id or str(uuid.uuid4())

                # Create agent request
                agent_request = AgentRequest(
                    user_input=request.message,
                    session_id=session_id,
                    language=request.language,
                    timestamp=start_time,
                )

                # Process request through Lumina
                response = await self.lumina_app.process_text_request(agent_request)

                processing_time = (datetime.now() - start_time).total_seconds()

                return ChatResponse(
                    response=response.content,
                    session_id=session_id,
                    language=request.language,
                    agent_used=response.agent_name,
                    confidence=response.confidence_score,
                    processing_time=processing_time,
                    success=response.success,
                    error=response.error,
                )

            except Exception as e:
                logger.error(f"Error in chat endpoint: {str(e)}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/health", response_model=HealthResponse)
        async def health_check():
            """Health check endpoint"""
            try:
                health_data = await self.lumina_app.health_check()

                # Determine overall status
                overall_status = "healthy"
                for component, data in health_data.items():
                    if isinstance(data, dict) and data.get("status") != "healthy":
                        overall_status = "unhealthy"
                        break

                return HealthResponse(
                    status=overall_status,
                    timestamp=datetime.now().isoformat(),
                    components=health_data,
                )

            except Exception as e:
                logger.error(f"Error in health check: {str(e)}")
                return HealthResponse(
                    status="unhealthy",
                    timestamp=datetime.now().isoformat(),
                    components={"error": str(e)},
                )

    def get_app(self) -> FastAPI:
        """Get FastAPI application"""
        return self.app


async def create_api_server(lumina_app: LuminaApp) -> LuminaAPI:
    """Create and configure API server"""
    try:
        api = LuminaAPI(lumina_app)
        logger.info("REST API server created successfully")
        return api

    except Exception as e:
        logger.error(f"Failed to create API server: {str(e)}")
        raise
