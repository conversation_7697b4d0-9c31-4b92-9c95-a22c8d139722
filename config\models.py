"""
Configuration models for Lumina AI
"""

from dataclasses import dataclass
from typing import Dict, Any


@dataclass
class LuminaSettings:
    """Application settings container"""

    app: Dict[str, Any]
    voice: Dict[str, Any]
    llm: Dict[str, Any]
    agents: Dict[str, Any]
    performance: Dict[str, Any]
    monitoring: Dict[str, Any]
    security: Dict[str, Any]
    storage: Dict[str, Any]
