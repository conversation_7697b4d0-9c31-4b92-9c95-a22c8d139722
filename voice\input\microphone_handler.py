"""
Microphone voice input handler for Lumina AI
Basic implementation using pyaudio for real microphone capture
"""

import asyncio
import logging
import threading
import time
from typing import Callable, Optional, Dict, Any

try:
    import pyaudio
    import numpy as np

    PYAUDIO_AVAILABLE = True
except ImportError:
    PYAUDIO_AVAILABLE = False

from .advanced_vad import AdvancedVAD, VoiceActivityType


class MicrophoneHandler:
    """Handles voice input through system microphone"""

    def __init__(self, config: Dict[str, Any], on_audio_received: Callable):
        self.config = config
        self.on_audio_received = on_audio_received
        self.logger = logging.getLogger(__name__)

        # Audio configuration
        self.sample_rate = config.get("input", {}).get("sample_rate", 16000)
        self.channels = config.get("input", {}).get("channels", 1)
        self.chunk_size = config.get("input", {}).get("chunk_size", 1024)
        self.silence_threshold = config.get("input", {}).get("silence_threshold", 0.01)
        self.silence_duration = config.get("input", {}).get("silence_duration", 2.0)

        # State
        self.is_recording = False
        self.is_listening = False
        self.audio_stream = None
        self.pyaudio_instance = None
        self.recording_thread = None
        self.event_loop = None

        # Voice activity detection
        self.audio_buffer = []
        self.silence_counter = 0
        self.voice_detected = False

        # Advanced VAD system
        self.advanced_vad = AdvancedVAD(
            {
                "sample_rate": self.sample_rate,
                "chunk_size": self.chunk_size,
                "volume_threshold": config.get("input", {}).get(
                    "silence_threshold", 0.01
                ),
                "speech_threshold": 0.6,
                "activation_threshold": 0.8,
                "analysis_window_size": 5,
                "enable_direction_detection": False,
                "activation_phrases": [
                    "lumina",
                    "hey lumina",
                    "ok lumina",
                    "लुमिना",
                    "हे लुमिना",
                ],
            }
        )
        self.vad_enabled = config.get("input", {}).get("advanced_vad", True)
        self.last_activity_type = VoiceActivityType.SILENCE

    async def initialize(self):
        """Initialize microphone handler"""
        try:
            self.logger.info("Initializing microphone voice handler...")

            if not PYAUDIO_AVAILABLE:
                self.logger.error(
                    "PyAudio not available. Install with: pip install pyaudio"
                )
                self.logger.warning(
                    "Microphone handler initialized (PyAudio not available)"
                )
                return False

            # Initialize PyAudio
            self.pyaudio_instance = pyaudio.PyAudio()

            # Test microphone access
            try:
                test_stream = self.pyaudio_instance.open(
                    format=pyaudio.paInt16,
                    channels=self.channels,
                    rate=self.sample_rate,
                    input=True,
                    frames_per_buffer=self.chunk_size,
                )
                test_stream.close()
                self.logger.info("Microphone access verified")
            except Exception as e:
                self.logger.error(f"Microphone access failed: {e}")
                return False

            self.logger.info("Microphone voice handler initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize microphone handler: {e}")
            return False

    async def start(self):
        """Start voice input processing"""
        try:
            if not PYAUDIO_AVAILABLE:
                self.logger.warning("Cannot start voice input - PyAudio not available")
                return

            self.logger.info("Starting microphone voice input...")

            # Store the current event loop for use in the recording thread
            self.event_loop = asyncio.get_running_loop()

            # Open audio stream
            self.audio_stream = self.pyaudio_instance.open(
                format=pyaudio.paInt16,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size,
                stream_callback=None,
            )

            self.is_listening = True

            # Start recording thread
            self.recording_thread = threading.Thread(
                target=self._recording_loop, daemon=True
            )
            self.recording_thread.start()

            self.logger.info("🎤 Microphone voice input activated - I'm listening!")

        except Exception as e:
            self.logger.error(f"Failed to start voice input: {e}")
            raise

    def _recording_loop(self):
        """Main recording loop (runs in separate thread)"""
        try:
            while self.is_listening and self.audio_stream:
                try:
                    # Read audio data
                    audio_data = self.audio_stream.read(
                        self.chunk_size, exception_on_overflow=False
                    )

                    # Convert to numpy array for processing
                    audio_array = np.frombuffer(audio_data, dtype=np.int16)

                    # Calculate volume level (RMS - Root Mean Square)
                    if len(audio_array) > 0:
                        # Calculate RMS volume safely
                        mean_square = np.mean(audio_array.astype(np.float64) ** 2)
                        volume = np.sqrt(max(0, mean_square))  # Ensure non-negative
                        normalized_volume = volume / 32768.0  # Normalize to 0-1
                    else:
                        normalized_volume = 0.0

                    # Advanced Voice Activity Detection
                    if self.vad_enabled:
                        # Schedule advanced VAD in the event loop
                        if self.event_loop and not self.event_loop.is_closed():
                            asyncio.run_coroutine_threadsafe(
                                self._run_advanced_vad(audio_data), self.event_loop
                            )
                    else:
                        # Fallback to simple volume-based detection
                        self._handle_simple_vad(normalized_volume, audio_data)

                    # Small delay to prevent excessive CPU usage
                    time.sleep(0.01)

                except Exception as e:
                    self.logger.error(f"Error in recording loop: {e}")
                    break

        except Exception as e:
            self.logger.error(f"Recording loop error: {e}")

    def _process_audio_buffer(self):
        """Process accumulated audio buffer"""
        try:
            if not self.audio_buffer:
                return

            # Combine all audio chunks
            combined_audio = b"".join(self.audio_buffer)

            self.logger.info("🤖 Processing voice input...")

            # Schedule the async processing in the main event loop
            if self.event_loop and not self.event_loop.is_closed():
                asyncio.run_coroutine_threadsafe(
                    self._async_process_audio(combined_audio), self.event_loop
                )

        except Exception as e:
            self.logger.error(f"Error processing audio buffer: {e}")

    async def _async_process_audio(self, audio_data: bytes):
        """Process audio data asynchronously"""
        try:
            # Generate a session ID (in real implementation, this would be managed properly)
            session_id = f"mic_session_{int(time.time())}"

            # Call the audio received callback
            await self.on_audio_received(audio_data, session_id)

        except Exception as e:
            self.logger.error(f"Error in async audio processing: {e}")

    async def cleanup(self):
        """Cleanup microphone resources"""
        try:
            self.logger.info("Cleaning up microphone voice handler...")

            # Stop listening
            self.is_listening = False

            # Close audio stream
            if self.audio_stream:
                self.audio_stream.stop_stream()
                self.audio_stream.close()
                self.audio_stream = None

            # Wait for recording thread to finish
            if self.recording_thread and self.recording_thread.is_alive():
                self.recording_thread.join(timeout=2.0)

            # Cleanup PyAudio
            if self.pyaudio_instance:
                self.pyaudio_instance.terminate()
                self.pyaudio_instance = None

            self.logger.info("Microphone voice handler cleanup complete")

        except Exception as e:
            self.logger.error(f"Error during microphone cleanup: {e}")

    def get_status(self) -> Dict[str, Any]:
        """Get handler status"""
        return {
            "is_listening": self.is_listening,
            "provider": "microphone",
            "status": "listening" if self.is_listening else "stopped",
            "functional": PYAUDIO_AVAILABLE,
            "description": (
                "Real microphone input capture"
                if PYAUDIO_AVAILABLE
                else "PyAudio not available"
            ),
            "requires": "pyaudio package" if not PYAUDIO_AVAILABLE else None,
            "config": {
                "sample_rate": self.sample_rate,
                "channels": self.channels,
                "chunk_size": self.chunk_size,
                "silence_threshold": self.silence_threshold,
                "silence_duration": self.silence_duration,
            },
        }

    async def _run_advanced_vad(self, audio_data: bytes):
        """Run advanced voice activity detection on audio data"""
        try:
            # Analyze audio with advanced VAD
            vad_result = await self.advanced_vad.analyze_audio(audio_data)

            # Handle the result
            self._handle_vad_result(vad_result, audio_data)

        except Exception as e:
            self.logger.error(f"Error in advanced VAD: {e}")

    def _handle_vad_result(self, vad_result, audio_data: bytes):
        """Handle advanced VAD result"""
        try:
            activity_type = vad_result.activity_type
            confidence = vad_result.confidence
            should_process = vad_result.should_process

            # Log activity changes
            if activity_type != self.last_activity_type:
                self.logger.info(
                    f"🎤 Voice Activity: {activity_type.value} (confidence: {confidence:.2f})"
                )
                self.last_activity_type = activity_type

            # Handle different activity types
            if activity_type == VoiceActivityType.DIRECT_SPEECH:
                if not self.voice_detected:
                    self.voice_detected = True
                    self.logger.info("🎤 Direct speech detected, processing...")

                self.audio_buffer.append(audio_data)
                self.silence_counter = 0

            elif activity_type == VoiceActivityType.ACTIVATION_PHRASE:
                if not self.voice_detected:
                    self.voice_detected = True
                    self.logger.info("🎤 Activation phrase detected!")

                self.audio_buffer.append(audio_data)
                self.silence_counter = 0

            elif activity_type == VoiceActivityType.AMBIENT_CONVERSATION:
                self.logger.debug("🎤 Ambient conversation detected (ignoring)")
                # Don't process ambient conversations

            elif activity_type == VoiceActivityType.BACKGROUND_NOISE:
                self.logger.debug("🎤 Background noise detected")
                # Handle silence for ongoing speech
                if self.voice_detected:
                    self.silence_counter += 1
                    self._check_silence_timeout()

            elif activity_type == VoiceActivityType.SILENCE:
                # Handle silence for ongoing speech
                if self.voice_detected:
                    self.silence_counter += 1
                    self._check_silence_timeout()

        except Exception as e:
            self.logger.error(f"Error handling VAD result: {e}")

    def _handle_simple_vad(self, normalized_volume: float, audio_data: bytes):
        """Handle simple volume-based voice activity detection"""
        try:
            if normalized_volume > self.silence_threshold:
                if not self.voice_detected:
                    self.voice_detected = True
                    self.logger.info("🎤 Voice detected (simple VAD), processing...")

                # Add to buffer
                self.audio_buffer.append(audio_data)
                self.silence_counter = 0
            else:
                # Silence detected
                if self.voice_detected:
                    self.silence_counter += 1
                    self._check_silence_timeout()

        except Exception as e:
            self.logger.error(f"Error in simple VAD: {e}")

    def _check_silence_timeout(self):
        """Check if silence duration has been exceeded"""
        try:
            silence_time = (self.silence_counter * self.chunk_size) / self.sample_rate
            if silence_time >= self.silence_duration:
                # Process accumulated audio
                if self.audio_buffer:
                    self._process_audio_buffer()

                # Reset for next voice input
                self.voice_detected = False
                self.silence_counter = 0
                self.audio_buffer = []

        except Exception as e:
            self.logger.error(f"Error checking silence timeout: {e}")
