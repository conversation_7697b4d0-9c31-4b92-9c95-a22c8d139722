agents:
  weather:
    name: "Weather Agent"
    description: "Provides weather information and forecasts"
    enabled: true
    priority: 1
    capabilities: ["weather"]
    keywords:
      hi: ["मौसम", "बारिश", "धूप", "ठंड", "गर्मी", "तापमान", "हवा", "बादल"]
      en: ["weather", "rain", "sunny", "cold", "hot", "temperature", "wind", "clouds"]
      mr: ["हवामान", "पाऊस", "उन्हाळा", "थंडी", "गरम", "तापमान", "वारा", "ढग"]
      gu: ["હવામાન", "વરસાદ", "સૂર્ય", "ઠંડક", "ગરમી", "તાપમાન", "પવન", "વાદળ"]
      ta: ["வானிலை", "மழை", "வெயில்", "குளிர்", "வெப்பம்", "வெப்பநிலை", "காற்று", "மேகம்"]
      te: ["వాతావరణం", "వర్షం", "ఎండ", "చల్లదనం", "వేడిమి", "ఉష్ణోగ్రత", "గాలి", "మేఘాలు"]
    confidence_threshold: 0.7
    
  reminder:
    name: "Reminder Agent"
    description: "Manages reminders and scheduling"
    enabled: true
    priority: 2
    capabilities: ["reminder", "scheduling"]
    keywords:
      hi: ["याद", "रिमाइंडर", "समय", "मीटिंग", "काम", "अपॉइंटमेंट", "कल", "आज"]
      en: ["remind", "reminder", "time", "meeting", "task", "appointment", "tomorrow", "today"]
      mr: ["आठवण", "रिमाइंडर", "वेळ", "मीटिंग", "काम", "भेट", "उद्या", "आज"]
      gu: ["યાદ", "રિમાઇન્ડર", "સમય", "મીટિંગ", "કામ", "મુલાકાત", "કાલે", "આજે"]
      ta: ["நினைவூட்டல்", "நேரம்", "கூட்டம்", "வேலை", "சந்திப்பு", "நாளை", "இன்று"]
      te: ["గుర్తుచేయు", "రిమైండర్", "సమయం", "మీటింగ్", "పని", "అపాయింట్మెంట్", "రేపు", "ఈరోజు"]
    confidence_threshold: 0.8
    
  news:
    name: "News Agent"
    description: "Fetches latest news and updates"
    enabled: true
    priority: 3
    capabilities: ["news", "updates"]
    keywords:
      hi: ["समाचार", "न्यूज़", "खबर", "ताज़ा", "अपडेट", "हेडलाइन", "ब्रेकिंग"]
      en: ["news", "latest", "update", "headline", "breaking", "current", "today"]
      mr: ["बातमी", "न्यूज", "ताज्या", "अपडेट", "हेडलाइन", "ब्रेकिंग"]
      gu: ["સમાચાર", "ન્યૂઝ", "તાજા", "અપડેટ", "હેડલાઇન", "બ્રેકિંગ"]
      ta: ["செய்தி", "புதிய", "புதுப்பிப்பு", "தலைப்பு", "முக்கிய"]
      te: ["వార్తలు", "న్యూస్", "తాజా", "అప్డేట్", "హెడ్లైన్", "బ్రేకింగ్"]
    confidence_threshold: 0.7
    
  summarization:
    name: "Summarization Agent"
    description: "Summarizes text and content"
    enabled: true
    priority: 4
    capabilities: ["summarization", "text_processing"]
    keywords:
      hi: ["सारांश", "संक्षेप", "छोटा", "मुख्य", "सार", "समरी"]
      en: ["summary", "summarize", "brief", "main", "key", "short", "essence"]
      mr: ["सारांश", "संक्षेप", "छोटा", "मुख्य", "सार"]
      gu: ["સારાંશ", "સંક્ષેપ", "નાનું", "મુખ્ય", "સાર"]
      ta: ["சுருக்கம்", "முக்கிய", "சிறிய", "சாராம்சம்"]
      te: ["సారాంశం", "సంక్షేపం", "చిన్న", "ముఖ్య", "సారం"]
    confidence_threshold: 0.6
    
  translation:
    name: "Translation Agent"
    description: "Translates text between languages"
    enabled: true
    priority: 5
    capabilities: ["translation", "language_conversion"]
    keywords:
      hi: ["अनुवाद", "ट्रांसलेट", "भाषा", "बदलना", "हिंदी", "अंग्रेजी"]
      en: ["translate", "translation", "language", "convert", "hindi", "english"]
      mr: ["भाषांतर", "ट्रांसलेट", "भाषा", "बदलणे", "हिंदी", "इंग्रजी"]
      gu: ["અનુવાદ", "ટ્રાન્સલેટ", "ભાષા", "બદલવું", "હિન્દી", "અંગ્રેજી"]
      ta: ["மொழிபெயர்ப்பு", "மொழி", "மாற்று", "தமிழ்", "ஆங்கிலம்"]
      te: ["అనువాదం", "ట్రాన్స్లేట్", "భాష", "మార్చు", "తెలుగు", "ఆంగ్లం"]
    confidence_threshold: 0.8
    
  general:
    name: "General Query Agent"
    description: "Handles general questions and conversations"
    enabled: true
    priority: 10  # Lowest priority - fallback agent
    capabilities: ["general_query", "conversation"]
    keywords:
      hi: ["क्या", "कैसे", "कहाँ", "कब", "कौन", "क्यों", "बताओ", "जानकारी"]
      en: ["what", "how", "where", "when", "who", "why", "tell", "information"]
      mr: ["काय", "कसे", "कुठे", "केव्हा", "कोण", "का", "सांगा", "माहिती"]
      gu: ["શું", "કેવી", "ક્યાં", "ક્યારે", "કોણ", "શા", "કહો", "માહિતી"]
      ta: ["என்ன", "எப்படி", "எங்கே", "எப்போது", "யார்", "ஏன்", "சொல்லு", "தகவல்"]
      te: ["ఏమిటి", "ఎలా", "ఎక్కడ", "ఎప్పుడు", "ఎవరు", "ఎందుకు", "చెప్పు", "సమాచారం"]
    confidence_threshold: 0.3

# Agent routing configuration
routing:
  strategy: "confidence_based"  # confidence_based, keyword_matching, hybrid
  fallback_agent: "general"
  max_agents_per_request: 3
  confidence_threshold: 0.5
  
  # Hybrid strategy weights
  weights:
    confidence_score: 0.7
    keyword_match: 0.2
    context_relevance: 0.1

# Agent communication patterns
communication:
  inter_agent_calls: true
  max_chain_length: 3
  timeout_per_agent: 15  # seconds
  
  # Agent dependencies
  dependencies:
    weather:
      requires: []
      can_call: ["translation"]
    reminder:
      requires: []
      can_call: ["translation"]
    news:
      requires: []
      can_call: ["summarization", "translation"]
    summarization:
      requires: []
      can_call: ["translation"]
    translation:
      requires: []
      can_call: []
    general:
      requires: []
      can_call: ["weather", "reminder", "news", "summarization", "translation"]
