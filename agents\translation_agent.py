"""
Translation Agent for Lumina AI

Provides multi-language translation capabilities.
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import re

from agents.base.agent_interface import BaseAgent, AgentRequest, AgentResponse, AgentCapability
from config.settings import get_settings
from utils.logging import get_logger

logger = get_logger(__name__)


class TranslationAgent(BaseAgent):
    """
    Agent for handling translation requests
    """
    
    def __init__(self):
        super().__init__()
        self.settings = get_settings()
        self.name = "translation"
        self.description = "Provides multi-language translation capabilities"
        self.version = "1.0.0"
        
        # Supported capabilities
        self.capabilities = [
            AgentCapability.TRANSLATION,
            AgentCapability.INFORMATION_RETRIEVAL
        ]
        
        # Keywords for intent detection
        self.keywords = {
            "hi": ["अनुवाद", "ट्रांसलेट", "भाषा बदलो", "में बदलो", "का मतलब"],
            "en": ["translate", "translation", "convert to", "in english", "in hindi", "meaning"],
            "mr": ["भाषांतर", "ट्रांसलेट", "भाषा बदला", "मध्ये बदला"],
            "gu": ["અનુવાદ", "ભાષા બદલો", "માં બદલો", "અર્થ"],
            "ta": ["மொழிபெயர்ப்பு", "மொழி மாற்று", "அர்த்தம்"],
            "te": ["అనువాదం", "భాష మార్చు", "అర్థం"]
        }
        
        # Supported languages
        self.supported_languages = {
            "hi": "Hindi",
            "en": "English", 
            "mr": "Marathi",
            "gu": "Gujarati",
            "ta": "Tamil",
            "te": "Telugu",
            "bn": "Bengali",
            "kn": "Kannada",
            "ml": "Malayalam",
            "pa": "Punjabi",
            "or": "Odia",
            "as": "Assamese"
        }
        
        # Language detection patterns
        self.language_patterns = {
            "hi": ["हिंदी", "हिन्दी", "hindi"],
            "en": ["english", "अंग्रेजी", "इंग्लिश"],
            "mr": ["marathi", "मराठी"],
            "gu": ["gujarati", "गुजराती", "ગુજરાતી"],
            "ta": ["tamil", "तमिल", "தமிழ்"],
            "te": ["telugu", "तेलुगु", "తెలుగు"]
        }
    
    async def initialize(self) -> bool:
        """Initialize the translation agent"""
        logger.info("Initializing Translation Agent")
        
        try:
            # Initialize translation resources
            logger.info("Translation agent initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize translation agent: {str(e)}")
            return False
    
    async def process_request(self, request: AgentRequest) -> AgentResponse:
        """Process a translation request"""
        try:
            user_input = request.user_input
            language = request.language
            
            # Extract text to translate and target language
            text_to_translate, source_lang, target_lang = self._extract_translation_request(user_input, language)
            
            if not text_to_translate:
                return AgentResponse(
                    content=self._get_text_prompt(language),
                    success=True,
                    agent_name=self.name,
                    timestamp=datetime.now(),
                    metadata={"requires_text": True}
                )
            
            if not target_lang:
                return AgentResponse(
                    content=self._get_language_prompt(language),
                    success=True,
                    agent_name=self.name,
                    timestamp=datetime.now(),
                    metadata={"requires_target_language": True}
                )
            
            # Perform translation
            translated_text = await self._translate_text(text_to_translate, source_lang, target_lang)
            
            if not translated_text:
                return AgentResponse(
                    content=self._get_error_message(language),
                    success=False,
                    agent_name=self.name,
                    timestamp=datetime.now()
                )
            
            # Format response
            response_text = self._format_translation_response(
                text_to_translate, translated_text, source_lang, target_lang, language
            )
            
            return AgentResponse(
                content=response_text,
                success=True,
                agent_name=self.name,
                timestamp=datetime.now(),
                metadata={
                    "source_language": source_lang,
                    "target_language": target_lang,
                    "original_text": text_to_translate,
                    "translated_text": translated_text
                }
            )
            
        except Exception as e:
            logger.error(f"Error processing translation request: {str(e)}")
            return AgentResponse(
                content=self._get_error_message(request.language),
                success=False,
                agent_name=self.name,
                timestamp=datetime.now(),
                error=str(e)
            )
    
    def can_handle_request(self, request: AgentRequest) -> float:
        """Check if this agent can handle the request"""
        user_input = request.user_input.lower()
        language = request.language
        
        # Check for translation-related keywords
        keywords = self.keywords.get(language, self.keywords["en"])
        
        for keyword in keywords:
            if keyword in user_input:
                return 0.8  # High confidence for translation keywords
        
        # Check for language names
        for lang_patterns in self.language_patterns.values():
            for pattern in lang_patterns:
                if pattern in user_input:
                    return 0.6  # Medium-high confidence for language mentions
        
        return 0.0
    
    def _extract_translation_request(self, user_input: str, language: str) -> Tuple[str, Optional[str], Optional[str]]:
        """Extract text to translate, source language, and target language"""
        text = user_input.lower()
        
        # Detect target language
        target_lang = self._detect_target_language(text)
        
        # Remove translation command words
        clean_text = self._clean_translation_text(user_input, language)
        
        # Auto-detect source language (simplified)
        source_lang = self._detect_source_language(clean_text, language)
        
        return clean_text, source_lang, target_lang
    
    def _detect_target_language(self, text: str) -> Optional[str]:
        """Detect target language from user input"""
        for lang_code, patterns in self.language_patterns.items():
            for pattern in patterns:
                if pattern in text:
                    # Check if it's mentioned as target (e.g., "translate to hindi")
                    if any(prep in text for prep in ["to ", "में ", "मध्ये ", "માં "]):
                        return lang_code
        
        return None
    
    def _detect_source_language(self, text: str, current_lang: str) -> Optional[str]:
        """Detect source language of the text"""
        # Simple heuristic - if text contains non-Latin characters, it's likely Indian language
        if re.search(r'[\u0900-\u097F]', text):  # Devanagari
            return "hi"
        elif re.search(r'[\u0A80-\u0AFF]', text):  # Gujarati
            return "gu"
        elif re.search(r'[\u0B80-\u0BFF]', text):  # Tamil
            return "ta"
        elif re.search(r'[\u0C00-\u0C7F]', text):  # Telugu
            return "te"
        elif re.search(r'[\u0D00-\u0D7F]', text):  # Malayalam
            return "ml"
        else:
            return "en"  # Default to English for Latin script
    
    def _clean_translation_text(self, user_input: str, language: str) -> str:
        """Clean translation command words from text"""
        text = user_input
        
        # Remove common translation phrases
        remove_phrases = {
            "hi": ["अनुवाद करो", "ट्रांसलेट करो", "भाषा बदलो", "में बदलो"],
            "en": ["translate", "translate to", "convert to", "in"]
        }
        
        phrases = remove_phrases.get(language, remove_phrases["en"])
        for phrase in phrases:
            text = re.sub(rf'\b{re.escape(phrase)}\b', '', text, flags=re.IGNORECASE).strip()
        
        # Remove language names
        for patterns in self.language_patterns.values():
            for pattern in patterns:
                text = re.sub(rf'\b{re.escape(pattern)}\b', '', text, flags=re.IGNORECASE).strip()
        
        return text.strip()
    
    async def _translate_text(self, text: str, source_lang: Optional[str], target_lang: str) -> Optional[str]:
        """Translate text from source to target language"""
        try:
            # Mock translation for demo purposes
            # In production, integrate with Google Translate API, Azure Translator, or local models
            
            if not text.strip():
                return None
            
            # Simple mock translations for demo
            mock_translations = {
                ("en", "hi"): {
                    "hello": "नमस्ते",
                    "good morning": "सुप्रभात",
                    "thank you": "धन्यवाद",
                    "how are you": "आप कैसे हैं"
                },
                ("hi", "en"): {
                    "नमस्ते": "hello",
                    "सुप्रभात": "good morning", 
                    "धन्यवाद": "thank you",
                    "आप कैसे हैं": "how are you"
                }
            }
            
            # Check for exact matches in mock data
            translation_dict = mock_translations.get((source_lang, target_lang), {})
            text_lower = text.lower().strip()
            
            if text_lower in translation_dict:
                return translation_dict[text_lower]
            
            # For demo, return a placeholder translation
            return f"[Translation of '{text}' from {source_lang} to {target_lang}]"
            
        except Exception as e:
            logger.error(f"Error translating text: {str(e)}")
            return None
    
    def _format_translation_response(self, original: str, translated: str, source_lang: Optional[str], target_lang: str, language: str) -> str:
        """Format the translation response"""
        source_name = self.supported_languages.get(source_lang, source_lang or "Unknown")
        target_name = self.supported_languages.get(target_lang, target_lang)
        
        headers = {
            "hi": f"🔄 अनुवाद ({source_name} → {target_name}):",
            "en": f"🔄 Translation ({source_name} → {target_name}):",
            "mr": f"🔄 भाषांतर ({source_name} → {target_name}):",
            "gu": f"🔄 અનુવાદ ({source_name} → {target_name}):",
            "ta": f"🔄 மொழிபெயர்ப்பு ({source_name} → {target_name}):",
            "te": f"🔄 అనువాదం ({source_name} → {target_name}):"
        }
        
        header = headers.get(language, headers["en"])
        
        return f"{header}\n\n📝 Original: {original}\n✨ Translation: {translated}"
    
    def _get_text_prompt(self, language: str) -> str:
        """Get text prompt message"""
        prompts = {
            "hi": "कृपया वह टेक्स्ट दें जिसका अनुवाद करना है।",
            "en": "Please provide the text you want me to translate.",
            "mr": "कृपया तो मजकूर द्या ज्याचे भाषांतर करायचे आहे.",
            "gu": "કૃપા કરીને તે ટેક્સ્ટ આપો જેનો અનુવાદ કરવો છે.",
            "ta": "தயவுசெய்து மொழிபெயர்க்க வேண்டிய உரையை வழங்கவும்.",
            "te": "దయచేసి అనువదించాల్సిన టెక్స్ట్ ఇవ్వండి."
        }
        
        return prompts.get(language, prompts["en"])
    
    def _get_language_prompt(self, language: str) -> str:
        """Get language prompt message"""
        prompts = {
            "hi": "कृपया बताएं कि किस भाषा में अनुवाद करना है? (जैसे: अंग्रेजी, हिंदी, मराठी)",
            "en": "Please specify which language to translate to? (e.g., Hindi, English, Marathi)",
            "mr": "कृपया सांगा की कोणत्या भाषेत भाषांतर करायचे? (जसे: हिंदी, इंग्रजी, मराठी)",
            "gu": "કૃપા કરીને કહો કે કઈ ભાષામાં અનુવાદ કરવો છે? (જેમ કે: હિન્દી, અંગ્રેજી, ગુજરાતી)",
            "ta": "எந்த மொழியில் மொழிபெயர்க்க வேண்டும் என்று சொல்லுங்கள்? (உதாரணம்: ஹிந்தி, ஆங்கிலம், தமிழ்)",
            "te": "ఏ భాషలోకి అనువదించాలో చెప్పండి? (ఉదాహరణ: హిందీ, ఇంగ్లీష్, తెలుగు)"
        }
        
        return prompts.get(language, prompts["en"])
    
    def _get_error_message(self, language: str) -> str:
        """Get error message"""
        messages = {
            "hi": "अनुवाद करने में समस्या हो रही है। कृपया बाद में कोशिश करें।",
            "en": "I'm having trouble with the translation. Please try again later.",
            "mr": "भाषांतर करण्यात अडचण येत आहे. कृपया नंतर प्रयत्न करा.",
            "gu": "અનુવાદ કરવામાં મુશ્કેલી આવી રહી છે. કૃપા કરીને પછીથી પ્રયાસ કરો.",
            "ta": "மொழிபெயர்ப்பில் சிக்கல் உள்ளது. பின்னர் முயற்சிக்கவும்.",
            "te": "అనువాదంలో ఇబ్బంది ఉంది. దయచేసి తర్వాత ప్రయత్నించండి."
        }
        
        return messages.get(language, messages["en"])
