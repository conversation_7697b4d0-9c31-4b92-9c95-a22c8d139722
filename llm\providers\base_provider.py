"""
Base LLM provider interface for Lumina AI
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import asyncio
import logging
import time


@dataclass
class LLMRequest:
    """Request data structure for LLM providers"""

    user_input: str
    language: str = "hi"
    session_id: Optional[str] = None
    max_tokens: int = 150
    temperature: float = 0.7
    context: Optional[Dict[str, Any]] = None
    system_prompt: Optional[str] = None
    conversation_history: Optional[List[Dict[str, Any]]] = None

    # Legacy alias for backward compatibility
    @property
    def prompt(self) -> str:
        """Alias for user_input"""
        return self.user_input


@dataclass
class LLMResponse:
    """Response data structure from LLM providers"""

    content: str
    success: bool
    provider: str
    model: str
    timestamp: Optional[Any] = None
    tokens_used: int = 0
    processing_time: float = 0.0
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

    # Legacy aliases for backward compatibility
    @property
    def text(self) -> str:
        """Alias for content"""
        return self.content

    @property
    def provider_name(self) -> str:
        """Alias for provider"""
        return self.provider

    @property
    def model_name(self) -> str:
        """Alias for model"""
        return self.model


class BaseLLMProvider(ABC):
    """Abstract base class for LLM providers"""

    def __init__(self, name: str, config: Dict[str, Any]):
        self.name = name
        self.config = config
        self.logger = logging.getLogger(f"llm.{name}")
        self._initialized = False
        self._health_status = "unknown"
        self._last_health_check = 0

    @abstractmethod
    async def generate(self, request: LLMRequest) -> LLMResponse:
        """
        Generate response from the LLM

        Args:
            request: The LLM request

        Returns:
            LLM response with generated text
        """
        pass

    async def generate_response(self, request: LLMRequest) -> LLMResponse:
        """
        Generate response from the LLM (alias for generate)

        Args:
            request: The LLM request

        Returns:
            LLM response with generated text
        """
        return await self.generate(request)

    async def initialize(self) -> bool:
        """
        Initialize the LLM provider

        Returns:
            True if initialization successful, False otherwise
        """
        try:
            self.logger.info(f"Initializing LLM provider: {self.name}")
            await self._initialize_client()
            self._initialized = True
            self._health_status = "healthy"
            self.logger.info(f"LLM provider {self.name} initialized successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize LLM provider {self.name}: {e}")
            self._health_status = "unhealthy"
            return False

    async def cleanup(self) -> None:
        """Cleanup provider resources"""
        try:
            self.logger.info(f"Cleaning up LLM provider: {self.name}")
            await self._cleanup_client()
            self._initialized = False
        except Exception as e:
            self.logger.error(f"Error during cleanup of LLM provider {self.name}: {e}")

    @abstractmethod
    async def _initialize_client(self) -> None:
        """Initialize provider-specific client (implemented by subclasses)"""
        pass

    async def _cleanup_client(self) -> None:
        """Cleanup provider-specific client (implemented by subclasses)"""
        pass

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the provider"""
        current_time = time.time()

        # Only perform actual health check if enough time has passed
        if current_time - self._last_health_check > 60:  # 1 minute
            try:
                # Simple test request
                test_request = LLMRequest(
                    user_input="Hello", language="en", max_tokens=5, temperature=0.1
                )

                start_time = time.time()
                response = await asyncio.wait_for(
                    self.generate(test_request), timeout=10.0
                )
                response_time = time.time() - start_time

                if response and response.text:
                    self._health_status = "healthy"
                else:
                    self._health_status = "unhealthy"

                self._last_health_check = current_time

                return {
                    "provider": self.name,
                    "status": self._health_status,
                    "response_time": response_time,
                    "last_check": current_time,
                    "initialized": self._initialized,
                }

            except Exception as e:
                self.logger.warning(f"Health check failed for {self.name}: {e}")
                self._health_status = "unhealthy"
                self._last_health_check = current_time

        return {
            "provider": self.name,
            "status": self._health_status,
            "last_check": self._last_health_check,
            "initialized": self._initialized,
        }

    def is_healthy(self) -> bool:
        """Check if provider is healthy"""
        return self._health_status == "healthy" and self._initialized

    def get_model_info(self) -> Dict[str, Any]:
        """Get model information"""
        return {
            "provider": self.name,
            "model": self.config.get("model", "unknown"),
            "max_tokens": self.config.get("max_tokens", 150),
            "temperature": self.config.get("temperature", 0.7),
        }

    def _create_system_prompt(
        self, language: str, context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create system prompt based on language and context"""
        base_prompts = {
            "hi": "आप एक सहायक AI असिस्टेंट हैं। कृपया हिंदी में संक्षिप्त और उपयोगी उत्तर दें।",
            "en": "You are a helpful AI assistant. Please provide concise and useful responses.",
            "mr": "तुम्ही एक उपयुक्त AI सहाय्यक आहात. कृपया मराठीत संक्षिप्त उत्तरे द्या.",
            "gu": "તમે એક ઉપયોગી AI સહાયક છો. કૃપા કરીને ગુજરાતીમાં સંક્ષિપ્ત જવાબો આપો.",
            "ta": "நீங்கள் ஒரு உதவிகரமான AI உதவியாளர். தமிழில் சுருக்கமான பதில்களை வழங்கவும்.",
            "te": "మీరు ఒక సహాయకరమైన AI సహాయకుడు. తెలుగులో సంక్షిప్త సమాధానాలు ఇవ్వండి.",
        }

        system_prompt = base_prompts.get(language, base_prompts["en"])

        # Add context-specific instructions
        if context:
            if context.get("agent_type"):
                agent_instructions = {
                    "weather": "Focus on weather-related information and forecasts.",
                    "reminder": "Help with scheduling and reminder management.",
                    "news": "Provide news summaries and updates.",
                    "summarization": "Create concise summaries of the given content.",
                    "translation": "Provide accurate translations between languages.",
                }

                agent_type = context["agent_type"]
                if agent_type in agent_instructions:
                    system_prompt += f" {agent_instructions[agent_type]}"

        return system_prompt

    def __str__(self) -> str:
        return f"LLMProvider({self.name}, model={self.config.get('model', 'unknown')})"

    def __repr__(self) -> str:
        return self.__str__()
