"""
Logging configuration for Lumina AI
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional
import json
from datetime import datetime
import os


# Color codes for terminal output
class Colors:
    """ANSI color codes for terminal output"""

    RESET = "\033[0m"
    BOLD = "\033[1m"
    DIM = "\033[2m"

    # Standard colors
    BLACK = "\033[30m"
    RED = "\033[31m"
    GREEN = "\033[32m"
    YELLOW = "\033[33m"
    BLUE = "\033[34m"
    MAGENTA = "\033[35m"
    CYAN = "\033[36m"
    WHITE = "\033[37m"

    # Bright colors
    BRIGHT_BLACK = "\033[90m"
    BRIGHT_RED = "\033[91m"
    BRIGHT_GREEN = "\033[92m"
    BRIGHT_YELLOW = "\033[93m"
    BRIGHT_BLUE = "\033[94m"
    BRIGHT_MAGENTA = "\033[95m"
    BRIGHT_CYAN = "\033[96m"
    BRIGHT_WHITE = "\033[97m"

    # Background colors
    BG_BLACK = "\033[40m"
    BG_RED = "\033[41m"
    BG_GREEN = "\033[42m"
    BG_YELLOW = "\033[43m"
    BG_BLUE = "\033[44m"
    BG_MAGENTA = "\033[45m"
    BG_CYAN = "\033[46m"
    BG_WHITE = "\033[47m"


class UserFriendlyFormatter(logging.Formatter):
    """User-friendly formatter with colors and emojis for production mode"""

    def __init__(self):
        super().__init__()
        self.use_colors = self._supports_color()

        # Emoji and color mapping for different log levels and components
        self.level_styles = {
            "DEBUG": {"emoji": "🔍", "color": Colors.BRIGHT_BLACK},
            "INFO": {"emoji": "✅", "color": Colors.BRIGHT_GREEN},
            "WARNING": {"emoji": "⚠️", "color": Colors.BRIGHT_YELLOW},
            "ERROR": {"emoji": "❌", "color": Colors.BRIGHT_RED},
            "CRITICAL": {"emoji": "🚨", "color": Colors.BG_RED + Colors.BRIGHT_WHITE},
        }

        # Component-specific emojis and colors
        self.component_styles = {
            "voice": {"emoji": "🎤", "color": Colors.BRIGHT_CYAN},
            "llm": {"emoji": "🤖", "color": Colors.BRIGHT_BLUE},
            "agent": {"emoji": "🎯", "color": Colors.BRIGHT_MAGENTA},
            "storage": {"emoji": "💾", "color": Colors.BRIGHT_GREEN},
            "metrics": {"emoji": "📊", "color": Colors.BRIGHT_YELLOW},
            "app": {"emoji": "🚀", "color": Colors.BRIGHT_WHITE},
            "config": {"emoji": "⚙️", "color": Colors.CYAN},
            "security": {"emoji": "🔒", "color": Colors.YELLOW},
            "default": {"emoji": "💡", "color": Colors.WHITE},
        }

    def _supports_color(self):
        """Check if terminal supports color output"""
        return (
            hasattr(sys.stdout, "isatty")
            and sys.stdout.isatty()
            and os.getenv("TERM") != "dumb"
            and os.name != "nt"
            or os.getenv("ANSICON") is not None
        )

    def _get_component_style(self, logger_name):
        """Get emoji and color for component based on logger name"""
        logger_lower = logger_name.lower()

        for component, style in self.component_styles.items():
            if component in logger_lower:
                return style

        return self.component_styles["default"]

    def _format_user_friendly_message(self, record):
        """Convert technical log messages to user-friendly ones"""
        message = record.getMessage()
        logger_name = record.name.lower()

        # Map technical messages to user-friendly ones
        if "initializing" in message.lower():
            if "lumina ai" in message.lower():
                return "🔧 Starting up Lumina AI..."
            elif "llm" in logger_name:
                return "🤖 Connecting to AI language models..."
            elif "voice" in logger_name:
                return "🎤 Setting up voice processing..."
            elif "agent" in logger_name:
                return "🎯 Preparing AI agents..."
            elif "memory" in logger_name or "storage" in logger_name:
                return "💾 Loading memory systems..."
            else:
                return f"🔧 Initializing {record.module}..."

        elif (
            "initialized successfully" in message.lower()
            or "loaded successfully" in message.lower()
        ):
            if "llm" in logger_name:
                return "✅ AI language models ready"
            elif "voice" in logger_name:
                return "✅ Voice processing ready"
            elif "agent" in logger_name:
                return "✅ AI agents ready"
            elif "memory" in logger_name or "storage" in logger_name:
                return "✅ Memory systems loaded"
            else:
                return f"✅ {record.module.title()} ready"

        elif "starting" in message.lower():
            if "main application loop" in message.lower():
                return "🚀 Lumina AI is now running and ready for conversations!"
            elif "voice input" in message.lower():
                if "placeholder" in message.lower():
                    return "⚠️ Voice input system starting (placeholder mode)..."
                else:
                    return "🎤 Voice input activated - I'm listening!"
            elif "metrics" in message.lower():
                return "📊 Performance monitoring started"
            else:
                return f"🔄 Starting {record.module}..."

        elif "cleanup" in message.lower() or "shutdown" in message.lower():
            return "👋 Shutting down gracefully..."

        elif "error" in message.lower() or record.levelname == "ERROR":
            return f"❌ {message}"

        elif "warning" in message.lower() or record.levelname == "WARNING":
            return f"⚠️ {message}"

        # Handle specific voice input messages
        elif "placeholder" in message.lower() and "voice" in message.lower():
            if "started" in message.lower():
                return "⚠️ Voice input in placeholder mode - real microphone capture not yet implemented"
            elif "initialized" in message.lower():
                return "🎤 Voice synthesizer initialized (placeholder)"

        # Handle special startup messages
        elif "lumina ai started successfully" in message.lower():
            return "💡 Lumina AI started successfully"

        # For other messages, add appropriate emoji based on component
        component_style = self._get_component_style(record.name)
        return f"{component_style['emoji']} {message}"

    def format(self, record):
        """Format log record in user-friendly way"""
        # Get timestamp
        timestamp = datetime.now().strftime("%H:%M:%S")

        # Get level style
        level_style = self.level_styles.get(record.levelname, self.level_styles["INFO"])

        # Get user-friendly message
        friendly_message = self._format_user_friendly_message(record)

        # Format the output
        if self.use_colors:
            if record.levelname in ["ERROR", "CRITICAL"]:
                # Errors get special formatting
                formatted = f"{Colors.BRIGHT_BLACK}[{timestamp}]{Colors.RESET} {level_style['color']}{level_style['emoji']} {friendly_message}{Colors.RESET}"
            elif record.levelname == "WARNING":
                # Warnings get yellow formatting
                formatted = f"{Colors.BRIGHT_BLACK}[{timestamp}]{Colors.RESET} {level_style['color']}{level_style['emoji']} {friendly_message}{Colors.RESET}"
            else:
                # Normal messages
                formatted = f"{Colors.BRIGHT_BLACK}[{timestamp}]{Colors.RESET} {level_style['emoji']} {friendly_message}"
        else:
            # No color support
            formatted = f"[{timestamp}] {level_style['emoji']} {friendly_message}"

        return formatted


class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging"""

    def format(self, record):
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }

        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)

        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in [
                "name",
                "msg",
                "args",
                "levelname",
                "levelno",
                "pathname",
                "filename",
                "module",
                "lineno",
                "funcName",
                "created",
                "msecs",
                "relativeCreated",
                "thread",
                "threadName",
                "processName",
                "process",
                "getMessage",
                "exc_info",
                "exc_text",
                "stack_info",
            ]:
                log_entry[key] = value

        return json.dumps(log_entry)


def setup_logging(
    level: str = "INFO",
    log_format: str = "json",
    log_file: Optional[str] = None,
    max_file_size: str = "10MB",
    backup_count: int = 5,
    debug_mode: bool = False,
):
    """Setup logging configuration for Lumina AI

    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_format: Format type ('json' for debug mode, 'user_friendly' for production)
        log_file: Optional file path for logging
        max_file_size: Maximum size of log file before rotation
        backup_count: Number of backup files to keep
        debug_mode: If True, use JSON logging; if False, use user-friendly logging
    """

    # Convert string level to logging constant
    numeric_level = getattr(logging, level.upper(), logging.INFO)

    # Create logs directory if it doesn't exist
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)

    # Clear existing handlers
    root_logger.handlers.clear()

    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(numeric_level)

    # Choose formatter based on debug mode
    if debug_mode or log_format.lower() == "json":
        console_formatter = JSONFormatter()
    else:
        console_formatter = UserFriendlyFormatter()

    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)

    # File handler (if specified)
    if log_file:
        # Parse max file size
        size_bytes = _parse_size(max_file_size)

        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=size_bytes, backupCount=backup_count, encoding="utf-8"
        )
        file_handler.setLevel(numeric_level)

        if log_format.lower() == "json":
            file_formatter = JSONFormatter()
        else:
            file_formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )

        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)

    # Set specific logger levels
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)

    logging.info("Logging configured successfully")


def _parse_size(size_str: str) -> int:
    """Parse size string (e.g., '10MB') to bytes"""
    size_str = size_str.upper().strip()

    if size_str.endswith("KB"):
        return int(size_str[:-2]) * 1024
    elif size_str.endswith("MB"):
        return int(size_str[:-2]) * 1024 * 1024
    elif size_str.endswith("GB"):
        return int(size_str[:-2]) * 1024 * 1024 * 1024
    else:
        # Assume bytes
        return int(size_str)


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance with the given name"""
    return logging.getLogger(name)


class ContextLogger:
    """Logger with context information"""

    def __init__(self, logger: logging.Logger, **context):
        self.logger = logger
        self.context = context

    def _log_with_context(self, level: int, message: str, **kwargs):
        """Log message with context"""
        extra = {**self.context, **kwargs}
        self.logger.log(level, message, extra=extra)

    def debug(self, message: str, **kwargs):
        self._log_with_context(logging.DEBUG, message, **kwargs)

    def info(self, message: str, **kwargs):
        self._log_with_context(logging.INFO, message, **kwargs)

    def warning(self, message: str, **kwargs):
        self._log_with_context(logging.WARNING, message, **kwargs)

    def error(self, message: str, **kwargs):
        self._log_with_context(logging.ERROR, message, **kwargs)

    def critical(self, message: str, **kwargs):
        self._log_with_context(logging.CRITICAL, message, **kwargs)


def create_context_logger(name: str, **context) -> ContextLogger:
    """Create a context logger with additional context information"""
    logger = logging.getLogger(name)
    return ContextLogger(logger, **context)
