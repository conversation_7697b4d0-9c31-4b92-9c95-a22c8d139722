"""
Advanced Voice Activity Detection for Lumina AI
Sophisticated audio analysis beyond simple volume thresholds
"""

import asyncio
import logging
import numpy as np
import time
from typing import Dict, List, Optional, Tuple, Any
from collections import deque
from dataclasses import dataclass
from enum import Enum

try:
    import scipy.signal
    from scipy.fft import fft
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False


class VoiceActivityType(Enum):
    """Types of voice activity detected"""
    SILENCE = "silence"
    BACKGROUND_NOISE = "background_noise"
    DIRECT_SPEECH = "direct_speech"
    AMBIENT_CONVERSATION = "ambient_conversation"
    ACTIVATION_PHRASE = "activation_phrase"


@dataclass
class VoiceActivityResult:
    """Result of voice activity detection"""
    activity_type: VoiceActivityType
    confidence: float  # 0.0 to 1.0
    audio_features: Dict[str, float]
    should_process: bool
    metadata: Dict[str, Any]


class AdvancedVAD:
    """Advanced Voice Activity Detection with intelligent audio analysis"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Audio parameters
        self.sample_rate = config.get("sample_rate", 16000)
        self.chunk_size = config.get("chunk_size", 1024)
        
        # Detection thresholds
        self.volume_threshold = config.get("volume_threshold", 0.01)
        self.speech_threshold = config.get("speech_threshold", 0.6)
        self.activation_threshold = config.get("activation_threshold", 0.8)
        
        # Analysis windows
        self.analysis_window_size = config.get("analysis_window_size", 5)  # seconds
        self.feature_history_size = int(self.analysis_window_size * self.sample_rate / self.chunk_size)
        
        # Feature history for pattern analysis
        self.volume_history = deque(maxlen=self.feature_history_size)
        self.spectral_history = deque(maxlen=self.feature_history_size)
        self.pitch_history = deque(maxlen=self.feature_history_size)
        self.energy_history = deque(maxlen=self.feature_history_size)
        
        # Activation phrases (can be expanded)
        self.activation_phrases = config.get("activation_phrases", [
            "lumina", "hey lumina", "ok lumina", "लुमिना", "हे लुमिना"
        ])
        
        # Background noise baseline
        self.background_noise_level = 0.0
        self.noise_adaptation_rate = 0.01
        self.calibration_samples = 0
        self.max_calibration_samples = 100
        
        # Speech pattern detection
        self.speech_pattern_buffer = deque(maxlen=20)
        self.last_speech_time = 0
        self.speech_continuity_threshold = 2.0  # seconds
        
        # Direction detection (basic implementation)
        self.enable_direction_detection = config.get("enable_direction_detection", False)
        self.direction_confidence = 0.5
    
    def _calculate_rms_volume(self, audio_data: bytes) -> float:
        """Calculate RMS volume with safe handling"""
        try:
            # Convert bytes to numpy array
            audio_array = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32)
            
            if len(audio_array) == 0:
                return 0.0
            
            # Normalize to [-1, 1] range
            audio_array = audio_array / 32768.0
            
            # Calculate RMS
            rms = np.sqrt(np.mean(audio_array ** 2))
            return float(rms)
            
        except Exception as e:
            self.logger.error(f"Error calculating RMS volume: {e}")
            return 0.0
    
    def _extract_spectral_features(self, audio_data: bytes) -> Dict[str, float]:
        """Extract spectral features from audio"""
        try:
            if not SCIPY_AVAILABLE:
                return {"spectral_centroid": 0.0, "spectral_rolloff": 0.0, "spectral_bandwidth": 0.0}
            
            # Convert to numpy array
            audio_array = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32)
            
            if len(audio_array) < 64:  # Minimum samples for FFT
                return {"spectral_centroid": 0.0, "spectral_rolloff": 0.0, "spectral_bandwidth": 0.0}
            
            # Normalize
            audio_array = audio_array / 32768.0
            
            # Apply window to reduce spectral leakage
            windowed = audio_array * np.hanning(len(audio_array))
            
            # Compute FFT
            fft_result = fft(windowed)
            magnitude = np.abs(fft_result[:len(fft_result)//2])
            
            # Frequency bins
            freqs = np.fft.fftfreq(len(windowed), 1/self.sample_rate)[:len(magnitude)]
            
            # Spectral centroid (brightness)
            if np.sum(magnitude) > 0:
                spectral_centroid = np.sum(freqs * magnitude) / np.sum(magnitude)
            else:
                spectral_centroid = 0.0
            
            # Spectral rolloff (85% of energy)
            cumsum = np.cumsum(magnitude)
            rolloff_threshold = 0.85 * cumsum[-1]
            rolloff_idx = np.where(cumsum >= rolloff_threshold)[0]
            spectral_rolloff = freqs[rolloff_idx[0]] if len(rolloff_idx) > 0 else 0.0
            
            # Spectral bandwidth
            if np.sum(magnitude) > 0:
                spectral_bandwidth = np.sqrt(np.sum(((freqs - spectral_centroid) ** 2) * magnitude) / np.sum(magnitude))
            else:
                spectral_bandwidth = 0.0
            
            return {
                "spectral_centroid": float(spectral_centroid),
                "spectral_rolloff": float(spectral_rolloff),
                "spectral_bandwidth": float(spectral_bandwidth)
            }
            
        except Exception as e:
            self.logger.error(f"Error extracting spectral features: {e}")
            return {"spectral_centroid": 0.0, "spectral_rolloff": 0.0, "spectral_bandwidth": 0.0}
    
    def _estimate_pitch(self, audio_data: bytes) -> float:
        """Estimate fundamental frequency (pitch) of audio"""
        try:
            if not SCIPY_AVAILABLE:
                return 0.0
            
            # Convert to numpy array
            audio_array = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32)
            
            if len(audio_array) < 512:  # Minimum samples for pitch detection
                return 0.0
            
            # Normalize
            audio_array = audio_array / 32768.0
            
            # Autocorrelation-based pitch detection
            correlation = np.correlate(audio_array, audio_array, mode='full')
            correlation = correlation[len(correlation)//2:]
            
            # Find peaks in autocorrelation
            min_period = int(self.sample_rate / 500)  # 500 Hz max
            max_period = int(self.sample_rate / 50)   # 50 Hz min
            
            if max_period >= len(correlation):
                return 0.0
            
            # Find the highest peak in the valid range
            valid_correlation = correlation[min_period:max_period]
            if len(valid_correlation) == 0:
                return 0.0
            
            peak_idx = np.argmax(valid_correlation) + min_period
            
            # Convert to frequency
            if peak_idx > 0:
                pitch = self.sample_rate / peak_idx
                return float(pitch)
            
            return 0.0
            
        except Exception as e:
            self.logger.error(f"Error estimating pitch: {e}")
            return 0.0
    
    def _detect_speech_patterns(self, features: Dict[str, float]) -> float:
        """Detect speech-like patterns in audio features"""
        try:
            # Speech typically has:
            # - Moderate to high spectral centroid (1000-4000 Hz)
            # - Pitch in human vocal range (80-400 Hz)
            # - Moderate spectral bandwidth
            # - Sufficient energy
            
            speech_score = 0.0
            
            # Spectral centroid check (speech brightness)
            centroid = features.get("spectral_centroid", 0)
            if 1000 <= centroid <= 4000:
                speech_score += 0.3
            elif 500 <= centroid <= 6000:
                speech_score += 0.1
            
            # Pitch check (human vocal range)
            pitch = features.get("pitch", 0)
            if 80 <= pitch <= 400:
                speech_score += 0.3
            elif 50 <= pitch <= 500:
                speech_score += 0.1
            
            # Energy check
            volume = features.get("volume", 0)
            if volume > self.volume_threshold * 2:
                speech_score += 0.2
            elif volume > self.volume_threshold:
                speech_score += 0.1
            
            # Spectral bandwidth check (speech has moderate bandwidth)
            bandwidth = features.get("spectral_bandwidth", 0)
            if 200 <= bandwidth <= 2000:
                speech_score += 0.2
            
            return min(1.0, speech_score)
            
        except Exception as e:
            self.logger.error(f"Error detecting speech patterns: {e}")
            return 0.0
    
    def _analyze_temporal_patterns(self) -> Dict[str, float]:
        """Analyze temporal patterns in audio features"""
        try:
            if len(self.volume_history) < 5:
                return {"continuity": 0.0, "variability": 0.0, "trend": 0.0}
            
            volumes = list(self.volume_history)
            
            # Continuity: how consistent is the audio level
            volume_std = np.std(volumes)
            continuity = 1.0 / (1.0 + volume_std * 10)  # Higher std = lower continuity
            
            # Variability: speech has natural variability
            variability = min(1.0, volume_std * 5)
            
            # Trend: is audio level increasing/decreasing
            if len(volumes) >= 3:
                trend = np.polyfit(range(len(volumes)), volumes, 1)[0]
                trend = np.tanh(trend * 100)  # Normalize to [-1, 1]
            else:
                trend = 0.0
            
            return {
                "continuity": float(continuity),
                "variability": float(variability),
                "trend": float(trend)
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing temporal patterns: {e}")
            return {"continuity": 0.0, "variability": 0.0, "trend": 0.0}
    
    def _update_background_noise(self, volume: float):
        """Adaptively update background noise level"""
        if self.calibration_samples < self.max_calibration_samples:
            # Initial calibration phase
            self.background_noise_level = (
                self.background_noise_level * self.calibration_samples + volume
            ) / (self.calibration_samples + 1)
            self.calibration_samples += 1
        else:
            # Adaptive update (only if volume is low)
            if volume < self.background_noise_level * 2:
                self.background_noise_level = (
                    self.background_noise_level * (1 - self.noise_adaptation_rate) +
                    volume * self.noise_adaptation_rate
                )
    
    async def analyze_audio(self, audio_data: bytes) -> VoiceActivityResult:
        """
        Perform advanced voice activity detection on audio data
        
        Args:
            audio_data: Raw audio bytes
            
        Returns:
            VoiceActivityResult with detection results
        """
        try:
            # Extract basic features
            volume = self._calculate_rms_volume(audio_data)
            spectral_features = self._extract_spectral_features(audio_data)
            pitch = self._estimate_pitch(audio_data)
            
            # Combine all features
            features = {
                "volume": volume,
                "pitch": pitch,
                **spectral_features
            }
            
            # Update feature history
            self.volume_history.append(volume)
            self.spectral_history.append(spectral_features.get("spectral_centroid", 0))
            self.pitch_history.append(pitch)
            
            # Update background noise baseline
            self._update_background_noise(volume)
            
            # Analyze patterns
            speech_score = self._detect_speech_patterns(features)
            temporal_patterns = self._analyze_temporal_patterns()
            
            # Determine activity type
            activity_type = VoiceActivityType.SILENCE
            confidence = 0.0
            should_process = False
            
            # Check for silence
            if volume < self.background_noise_level * 1.5:
                activity_type = VoiceActivityType.SILENCE
                confidence = 1.0 - (volume / (self.background_noise_level * 1.5))
            
            # Check for background noise
            elif volume < self.volume_threshold:
                activity_type = VoiceActivityType.BACKGROUND_NOISE
                confidence = 0.8
            
            # Check for speech
            elif speech_score > self.speech_threshold:
                # Determine if it's direct speech or ambient conversation
                directness_score = (
                    speech_score * 0.4 +
                    temporal_patterns["continuity"] * 0.3 +
                    temporal_patterns["variability"] * 0.3
                )
                
                if directness_score > 0.7:
                    activity_type = VoiceActivityType.DIRECT_SPEECH
                    confidence = directness_score
                    should_process = True
                    self.last_speech_time = time.time()
                else:
                    activity_type = VoiceActivityType.AMBIENT_CONVERSATION
                    confidence = speech_score
            
            # Check for activation phrases (simplified - would need actual speech recognition)
            elif speech_score > 0.4 and volume > self.volume_threshold * 3:
                activity_type = VoiceActivityType.ACTIVATION_PHRASE
                confidence = speech_score
                should_process = True
            
            # Create result
            result = VoiceActivityResult(
                activity_type=activity_type,
                confidence=confidence,
                audio_features=features,
                should_process=should_process,
                metadata={
                    "background_noise_level": self.background_noise_level,
                    "speech_score": speech_score,
                    "temporal_patterns": temporal_patterns,
                    "calibration_complete": self.calibration_samples >= self.max_calibration_samples
                }
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in advanced VAD analysis: {e}")
            return VoiceActivityResult(
                activity_type=VoiceActivityType.SILENCE,
                confidence=0.0,
                audio_features={},
                should_process=False,
                metadata={"error": str(e)}
            )
