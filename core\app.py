"""
Lumina AI Core Application
Main application orchestrator
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime

# First party imports
from voice.input.livekit_handler import Live<PERSON>itVoiceHandler
from voice.input.microphone_handler import <PERSON><PERSON><PERSON>andler
from voice.output.voice_synthesizer import VoiceSynthesizer
from voice.voice_coordinator import VoiceCoordinator
from voice.output.audio_player import AudioPlayer
from voice.greeting_system import GreetingSystem
from voice.conversation.session_handler import VoiceSessionHandler
from llm.router import LLMRouter
from agents.base.agent_router import AgentRouter
from utils.metrics import MetricsCollector
from storage.memory_store import MemoryStore
from config.models import LuminaSettings

# Local imports
from .context_manager import ContextManager
from .exceptions import LuminaException


class LuminaApp:
    """Main Lumina AI application class"""

    def __init__(self, settings: LuminaSettings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)

        # Core components
        self.context_manager: Optional[ContextManager] = None
        self.voice_handler: Optional[LiveKitVoiceHandler] = None
        self.voice_synthesizer: Optional[VoiceSynthesizer] = None
        self.voice_coordinator: Optional[VoiceCoordinator] = None
        self.audio_player: Optional[AudioPlayer] = None
        self.greeting_system: Optional[GreetingSystem] = None
        self.voice_session_handler: Optional[VoiceSessionHandler] = None
        self.llm_router: Optional[LLMRouter] = None
        self.agent_router: Optional[AgentRouter] = None
        self.metrics: Optional[MetricsCollector] = None
        self.storage: Optional[MemoryStore] = None

        # Application state
        self.is_running = False
        self.active_sessions: Dict[str, Dict[str, Any]] = {}

    async def initialize(self):
        """Initialize all application components"""
        try:
            self.logger.info("Initializing Lumina AI components...")

            # Initialize storage
            self.storage = MemoryStore(self.settings.storage)
            await self.storage.initialize()

            # Initialize context manager
            self.context_manager = ContextManager(self.storage)

            # Initialize metrics collector
            self.metrics = MetricsCollector()

            # Initialize LLM router
            self.llm_router = LLMRouter()
            await self.llm_router.initialize()

            # Initialize agent router
            self.agent_router = AgentRouter()
            await self.agent_router.initialize()

            # Initialize voice components
            self.voice_synthesizer = VoiceSynthesizer(self.settings.voice)
            await self.voice_synthesizer.initialize()

            # Initialize voice coordinator (working TTS system)
            self.voice_coordinator = VoiceCoordinator()
            await self.voice_coordinator.initialize()

            # Initialize audio player
            self.audio_player = AudioPlayer(self.settings.voice.get("output", {}))
            await self.audio_player.initialize()

            # Initialize greeting system
            self.greeting_system = GreetingSystem(
                self.voice_coordinator, self.audio_player
            )

            # Initialize voice session handler for conversation management
            self.voice_session_handler = VoiceSessionHandler(
                self.voice_coordinator, self.llm_router, self.agent_router
            )

            # Initialize voice input handler based on configuration
            voice_provider = self.settings.voice.get("input", {}).get(
                "provider", "livekit"
            )

            if voice_provider == "microphone":
                self.voice_handler = MicrophoneHandler(
                    self.settings.voice, self._on_voice_input
                )
            else:
                # Default to LiveKit for other providers
                self.voice_handler = LiveKitVoiceHandler(
                    self.settings.voice, self._on_voice_input
                )

            await self.voice_handler.initialize()

            self.logger.info("All components initialized successfully")
            return True

        except Exception as e:
            self.logger.error("Failed to initialize components: %s", e)
            raise LuminaException(f"Initialization failed: {e}") from e

    async def play_startup_greeting(self) -> bool:
        """Play automatic startup greeting after initialization"""
        try:
            if not self.greeting_system:
                self.logger.warning("Greeting system not available")
                return False

            # Play the greeting
            success = await self.greeting_system.play_startup_greeting()

            if success:
                self.logger.info("🎉 Startup greeting completed successfully")
            else:
                self.logger.warning("⚠️ Startup greeting failed")

            return success

        except Exception as e:
            self.logger.error(f"Error playing startup greeting: {e}")
            return False

    async def run(self):
        """Run the main application loop"""
        try:
            self.is_running = True
            self.logger.info("Starting main application loop...")

            # Start voice handler
            await self.voice_handler.start()

            # Start metrics collection
            if self.settings.monitoring.get("metrics", {}).get("enabled", False):
                asyncio.create_task(self.metrics.start_collection())

            # Main application loop
            while self.is_running:
                await asyncio.sleep(1)  # Keep the loop running

        except Exception as e:
            self.logger.error("Application loop error: %s", e)
            raise

    async def run_with_timeout(self, timeout_seconds: int = 30):
        """Run the application for a specific duration (for testing)"""
        try:
            self.is_running = True
            self.logger.info(
                "Starting main application loop with %d second timeout...",
                timeout_seconds,
            )

            # Start voice handler
            await self.voice_handler.start()

            # Start metrics collection
            if self.settings.monitoring.get("metrics", {}).get("enabled", False):
                asyncio.create_task(self.metrics.start_collection())

            # Run for specified duration
            for i in range(timeout_seconds):
                if not self.is_running:
                    break
                remaining = timeout_seconds - i
                if remaining % 10 == 0 or remaining <= 5:
                    self.logger.info(
                        "Application running... %d seconds remaining", remaining
                    )
                await asyncio.sleep(1)

            self.logger.info(
                "✅ Application timeout reached - Lumina AI is working correctly!"
            )
            self.logger.info(
                "🎉 For production mode, use app.run() instead of run_with_timeout()"
            )

        except Exception as e:
            self.logger.error("Application loop error: %s", e)
            raise

    async def cleanup(self):
        """Cleanup application resources"""
        try:
            self.logger.info("Cleaning up application resources...")
            self.is_running = False

            if self.voice_handler:
                await self.voice_handler.cleanup()

            if self.voice_synthesizer:
                await self.voice_synthesizer.cleanup()

            if self.voice_coordinator:
                await self.voice_coordinator.cleanup()

            if self.audio_player:
                await self.audio_player.cleanup()

            if self.agent_router:
                await self.agent_router.cleanup()

            if self.llm_router:
                await self.llm_router.cleanup()

            if self.storage:
                await self.storage.cleanup()

            self.logger.info("Cleanup completed")

        except Exception as e:
            self.logger.error("Cleanup error: %s", e)

    async def _on_voice_input(self, audio_data: bytes, session_id: str):
        """Handle incoming voice input"""
        try:
            start_time = asyncio.get_event_loop().time()

            # Process voice input through the pipeline
            response = await self._process_voice_request(audio_data, session_id)

            # Record metrics
            processing_time = asyncio.get_event_loop().time() - start_time
            agent_name = response.get("agent_used", "unknown")
            status = "error" if response.get("error", False) else "success"
            self.metrics.record_request(
                agent=agent_name,
                status=status,
                response_time=processing_time,
            )

        except Exception as e:
            self.logger.error("Voice input processing error: %s", e)
            # Send error response
            await self._send_error_response(session_id, str(e))

    async def _process_voice_request(
        self, audio_data: bytes, session_id: str
    ) -> Dict[str, Any]:
        """Process a voice request through the complete pipeline"""
        try:
            if not self.voice_session_handler:
                return {
                    "response": "Voice session handler not available",
                    "agent_used": "none",
                    "error": True,
                }

            # Process through the voice session handler
            result = await self.voice_session_handler.process_voice_input(
                audio_data, language="hi"
            )

            return {
                "response": result.get("response_text", ""),
                "agent_used": "voice_session",
                "error": not result.get("success", False),
                "transcribed_text": result.get("transcribed_text", ""),
                "confidence": result.get("confidence", 0.0),
                "processing_time": result.get("processing_time", 0.0),
            }

        except Exception as e:
            self.logger.error(f"Error processing voice request: {e}")
            return {
                "response": f"Error processing voice: {str(e)}",
                "agent_used": "error",
                "error": True,
            }

    async def _send_error_response(self, session_id: str, error_message: str):
        """Send error response to user"""
        # This is a placeholder - will be implemented with actual voice synthesis
        self.logger.warning(
            "Sending error response to %s: %s", session_id, error_message
        )

    def get_status(self) -> Dict[str, Any]:
        """Get application status"""
        return {
            "is_running": self.is_running,
            "active_sessions": len(self.active_sessions),
            "components": {
                "voice_handler": self.voice_handler is not None,
                "voice_synthesizer": self.voice_synthesizer is not None,
                "llm_router": self.llm_router is not None,
                "agent_router": self.agent_router is not None,
                "storage": self.storage is not None,
            },
        }

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on all components"""
        health_status = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "healthy",
            "components": {},
        }

        try:
            # Check storage
            if self.storage:
                health_status["components"]["storage"] = {
                    "status": "healthy",
                    "details": "Memory store operational",
                }
            else:
                health_status["components"]["storage"] = {
                    "status": "unhealthy",
                    "details": "Storage not initialized",
                }

            # Check LLM router
            if self.llm_router:
                provider_count = len(self.llm_router.providers)
                health_status["components"]["llm_router"] = {
                    "status": "healthy",
                    "details": f"Router operational with {provider_count} providers",
                }
            else:
                health_status["components"]["llm_router"] = {
                    "status": "unhealthy",
                    "details": "LLM router not initialized",
                }

            # Check agent router
            if self.agent_router:
                agent_count = len(self.agent_router.agents)
                health_status["components"]["agent_router"] = {
                    "status": "healthy",
                    "details": f"Agent router operational with {agent_count} agents",
                }
            else:
                health_status["components"]["agent_router"] = {
                    "status": "unhealthy",
                    "details": "Agent router not initialized",
                }

            # Check voice components
            if self.voice_handler:
                health_status["components"]["voice_handler"] = {
                    "status": "healthy",
                    "details": "Voice handler operational",
                }
            else:
                health_status["components"]["voice_handler"] = {
                    "status": "unhealthy",
                    "details": "Voice handler not initialized",
                }

            if self.voice_synthesizer:
                health_status["components"]["voice_synthesizer"] = {
                    "status": "healthy",
                    "details": "Voice synthesizer operational",
                }
            else:
                health_status["components"]["voice_synthesizer"] = {
                    "status": "unhealthy",
                    "details": "Voice synthesizer not initialized",
                }

            # Determine overall status
            unhealthy_components = [
                name
                for name, status in health_status["components"].items()
                if status["status"] == "unhealthy"
            ]

            if unhealthy_components:
                health_status["overall_status"] = "degraded"
                health_status["unhealthy_components"] = unhealthy_components

        except Exception as e:
            self.logger.error("Health check failed: %s", e)
            health_status["overall_status"] = "error"
            health_status["error"] = str(e)

        return health_status
