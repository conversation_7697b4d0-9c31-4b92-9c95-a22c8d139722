"""
Input Validation and Sanitization for Lumina AI

Provides validation and sanitization functions for user inputs and system data.
"""

import re
import html
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlparse

from utils.logging import get_logger

logger = get_logger(__name__)


class InputValidator:
    """
    Validates and sanitizes user inputs
    """

    # Common patterns
    EMAIL_PATTERN = re.compile(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")
    PHONE_PATTERN = re.compile(r"^[\+]?[1-9][\d]{0,15}$")
    URL_PATTERN = re.compile(
        r"^https?://(?:[-\w.])+(?:\:[0-9]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?$"
    )

    # Dangerous patterns to filter
    SCRIPT_PATTERN = re.compile(r"<script[^>]*>.*?</script>", re.IGNORECASE | re.DOTALL)
    SQL_INJECTION_PATTERN = re.compile(
        r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
        re.IGNORECASE,
    )

    @staticmethod
    def sanitize_text(text: str, max_length: int = 1000) -> str:
        """Sanitize text input"""
        if not isinstance(text, str):
            return ""

        # Remove HTML tags and escape HTML entities
        text = html.escape(text)

        # Remove script tags
        text = InputValidator.SCRIPT_PATTERN.sub("", text)

        # Limit length
        if len(text) > max_length:
            text = text[:max_length]

        # Remove excessive whitespace
        text = re.sub(r"\s+", " ", text).strip()

        return text
