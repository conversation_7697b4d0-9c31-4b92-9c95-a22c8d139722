#!/usr/bin/env python3
"""
Lumina AI Demo Script
Demonstrates core functionality without requiring API keys
"""
import asyncio
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.app import <PERSON><PERSON>App
from config.settings import get_settings
from utils.logging import setup_logging
import logging


async def demo_memory_store(app):
    """Demo memory store functionality"""
    print("\n🧠 Memory Store Demo:")
    print("=" * 40)

    # Test cache functionality
    await app.storage.cache_set("user_name", "राज", ttl=3600)
    await app.storage.cache_set("language", "hindi", ttl=3600)

    name = await app.storage.cache_get("user_name")
    lang = await app.storage.cache_get("language")

    print(f"✅ Cached user name: {name}")
    print(f"✅ Cached language: {lang}")

    # Test conversation storage
    session_id = "demo_session_123"
    await app.storage.store_conversation_turn(
        session_id=session_id,
        user_input="नमस्ते, मैं राज हूं",
        agent_response="नमस्ते राज! मैं लुमिना एआई हूं। आपकी कैसे सहायता कर सकती हूं?",
        metadata={"language": "hi", "confidence": 0.95},
    )

    history = await app.storage.get_conversation_history(session_id)
    print(f"✅ Conversation stored: {len(history)} turns")
    if history:
        turn = history[0]
        print(f"   User: {turn['user_input']}")
        print(f"   AI: {turn['agent_response']}")

    # Test user preferences
    await app.storage.store_user_preference("raj", "preferred_language", "hi")
    await app.storage.store_user_preference("raj", "voice_speed", "normal")

    pref_lang = await app.storage.get_user_preference("raj", "preferred_language")
    voice_speed = await app.storage.get_user_preference("raj", "voice_speed")

    print(f"✅ User preferences stored:")
    print(f"   Language: {pref_lang}")
    print(f"   Voice Speed: {voice_speed}")


async def demo_llm_router(app):
    """Demo LLM router functionality"""
    print("\n🤖 LLM Router Demo:")
    print("=" * 40)

    router = app.llm_router
    print(f"✅ Router initialized with {len(router.providers)} providers")

    for name, provider in router.providers.items():
        print(f"   • {name}: {'Ready' if provider.is_healthy else 'Not configured'}")
        print(f"     Model: {provider.model_name}")
        print(f"     Max Tokens: {provider.max_tokens}")
        print(f"     Temperature: {provider.temperature}")

    # Show circuit breaker status
    print(f"\n🔧 Circuit Breaker Status:")
    for name, breaker in router.circuit_breakers.items():
        print(f"   • {name}: {breaker.state}")


async def demo_agent_system(app):
    """Demo agent system functionality"""
    print("\n🎯 Agent System Demo:")
    print("=" * 40)

    agent_router = app.agent_router
    print(f"✅ Agent Router initialized")
    print(f"   Registered agents: {len(agent_router.agents)}")

    # Show available agent types (even if not loaded)
    agent_types = ["weather", "news", "translation", "summarization", "reminder"]

    print(f"   Available agent types: {', '.join(agent_types)}")

    # Show routing configuration
    print(f"   Default agent: {agent_router.default_agent}")
    print(f"   Confidence threshold: {agent_router.confidence_threshold}")


async def demo_voice_system(app):
    """Demo voice system functionality"""
    print("\n🎤 Voice System Demo:")
    print("=" * 40)

    # Voice synthesizer
    synthesizer = app.voice_synthesizer
    print(f"✅ Voice Synthesizer: {synthesizer.provider}")

    # Voice handler
    handler = app.voice_handler
    print(f"✅ Voice Handler: livekit")

    # Show voice mapping
    settings = get_settings()
    voice_mapping = settings.voice.get("output", {}).get("voice_mapping", {})

    print(f"\n🗣️ Voice Mapping:")
    for lang, voice in voice_mapping.items():
        print(f"   • {lang}: {voice}")


async def demo_configuration(app):
    """Demo configuration system"""
    print("\n⚙️ Configuration Demo:")
    print("=" * 40)

    settings = get_settings()

    print(f"✅ App Configuration:")
    print(f"   Name: {settings.app.get('name')}")
    print(f"   Version: {settings.app.get('version')}")
    print(f"   Debug: {settings.app.get('debug')}")
    print(f"   Default Language: {settings.app.get('default_language')}")

    print(f"\n🌍 Supported Languages:")
    for lang in settings.app.get("supported_languages", []):
        print(f"   • {lang}")

    print(f"\n🔧 LLM Configuration:")
    llm_config = settings.llm.get("providers", {})
    for tier, config in llm_config.items():
        print(f"   {tier.title()}: {config.get('name')} ({config.get('model')})")


async def main():
    """Main demo function"""
    print("🌟" * 30)
    print("🎉 LUMINA AI LIVE DEMO")
    print("🌟" * 30)

    # Setup logging (quieter for demo)
    setup_logging()
    logging.getLogger().setLevel(logging.WARNING)

    try:
        # Initialize Lumina
        print("\n🚀 Initializing Lumina AI...")
        settings = get_settings()
        app = LuminaApp(settings)
        await app.initialize()
        print("✅ Lumina AI initialized successfully!")

        # Run demos
        await demo_configuration(app)
        await demo_memory_store(app)
        await demo_llm_router(app)
        await demo_agent_system(app)
        await demo_voice_system(app)

        # Show final status
        print("\n" + "🌟" * 30)
        print("📊 LUMINA AI STATUS SUMMARY")
        print("🌟" * 30)

        storage_status = app.storage.get_status()
        print(
            f"💾 Storage: {storage_status['conversations_count']} conversations, "
            f"{storage_status['cache_entries']} cache entries"
        )

        print(f"🤖 LLM: {len(app.llm_router.providers)} providers ready")
        print(f"🎯 Agents: {len(app.agent_router.agents)} agents loaded")
        print(f"🎤 Voice: {app.voice_synthesizer.provider} synthesizer ready")

        print(f"\n✨ Lumina AI is fully operational and ready for:")
        print(f"   • Multilingual voice conversations")
        print(f"   • Intelligent agent routing")
        print(f"   • Conversation memory")
        print(f"   • User preference management")
        print(f"   • Real-time voice synthesis")

        print(f"\n📝 To enable full functionality:")
        print(f"   1. Set GEMINI_API_KEY environment variable")
        print(f"   2. Set GROQ_API_KEY environment variable")
        print(f"   3. Configure LiveKit for real-time voice")
        print(f"   4. Load specific agents for tasks")

        # Cleanup
        await app.cleanup()
        print(f"\n✅ Demo completed successfully!")

        return True

    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    print(f"\n{'🎉 SUCCESS!' if success else '❌ FAILED'}")
    sys.exit(0 if success else 1)
